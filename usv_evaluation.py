"""
USV模型评估脚本 - 测试和比较不同模型的性能
"""
import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import List, Dict, Optional
import time

from usv_core import USVEnv
from imitation_learning import ImitationActor, FeatureProcessor
from rl_enhanced_training import SingleUSVEnv, EnhancedTD3Agent, EnhancedRLConfig
from train_td3 import TD3Agent, TD3Config


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, num_episodes: int = 10, max_steps: int = 2000, render: bool = False):
        self.num_episodes = num_episodes
        self.max_steps = max_steps
        self.render = render
        self.feature_processor = FeatureProcessor()
        
    def evaluate_apf_baseline(self) -> Dict[str, float]:
        """评估APF基线性能"""
        print("Evaluating APF baseline...")
        
        env = USVEnv(dt=0.1)
        results = []
        
        for episode in range(self.num_episodes):
            env.reset()
            episode_reward = 0.0
            episode_length = 0
            success = False
            
            for step in range(self.max_steps):
                if self.render:
                    env.render()
                    time.sleep(0.05)
                
                obs, reward, done, info = env.step()
                episode_reward += reward
                episode_length += 1
                
                if done:
                    success = info.get('success', False)
                    break
            
            results.append({
                'episode': episode + 1,
                'reward': episode_reward,
                'length': episode_length,
                'success': success
            })
            
            print(f"APF Episode {episode+1}: Reward={episode_reward:.2f}, Length={episode_length}, Success={success}")
        
        return self._compute_metrics(results, "APF")
    
    def evaluate_imitation_model(self, model_path: str) -> Dict[str, float]:
        """评估模仿学习模型"""
        print(f"Evaluating imitation model: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"Model file not found: {model_path}")
            return {}
        
        # 加载模型
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        checkpoint = torch.load(model_path, map_location=device)
        
        obs_dim = self.feature_processor.get_feature_dim()
        model = ImitationActor(obs_dim, 2).to(device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # 评估
        env = SingleUSVEnv(max_time_s=200.0)
        results = []
        
        for episode in range(self.num_episodes):
            obs = env.reset()
            episode_reward = 0.0
            episode_length = 0
            success = False
            
            for step in range(self.max_steps):
                if self.render:
                    env.render()
                    time.sleep(0.05)
                
                # 使用模仿学习模型选择动作
                with torch.no_grad():
                    obs_tensor = torch.FloatTensor(obs).unsqueeze(0).to(device)
                    action = model(obs_tensor).cpu().numpy()[0]
                
                obs, reward, done, info = env.step(action)
                episode_reward += reward
                episode_length += 1
                
                if done:
                    success = info.get('success', False)
                    break
            
            results.append({
                'episode': episode + 1,
                'reward': episode_reward,
                'length': episode_length,
                'success': success
            })
            
            print(f"Imitation Episode {episode+1}: Reward={episode_reward:.2f}, Length={episode_length}, Success={success}")
        
        return self._compute_metrics(results, "Imitation")
    
    def evaluate_rl_model(self, model_path: str) -> Dict[str, float]:
        """评估强化学习模型"""
        print(f"Evaluating RL model: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"Model file not found: {model_path}")
            return {}
        
        # 配置和加载模型
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        obs_dim = self.feature_processor.get_feature_dim()
        
        config = EnhancedRLConfig(
            obs_dim=obs_dim,
            act_dim=2,
            act_limit=1.0,
            device=device.type
        )
        
        agent = EnhancedTD3Agent(config)
        agent.load(model_path)
        
        # 评估
        env = SingleUSVEnv(max_time_s=200.0)
        results = []
        
        for episode in range(self.num_episodes):
            obs = env.reset()
            episode_reward = 0.0
            episode_length = 0
            success = False
            
            for step in range(self.max_steps):
                if self.render:
                    env.render()
                    time.sleep(0.05)
                
                # 使用RL模型选择动作（无噪声）
                action = agent.act(obs, noise_std=0.0)
                
                obs, reward, done, info = env.step(action)
                episode_reward += reward
                episode_length += 1
                
                if done:
                    success = info.get('success', False)
                    break
            
            results.append({
                'episode': episode + 1,
                'reward': episode_reward,
                'length': episode_length,
                'success': success
            })
            
            print(f"RL Episode {episode+1}: Reward={episode_reward:.2f}, Length={episode_length}, Success={success}")
        
        return self._compute_metrics(results, "RL")
    
    def _compute_metrics(self, results: List[Dict], model_name: str) -> Dict[str, float]:
        """计算评估指标"""
        rewards = [r['reward'] for r in results]
        lengths = [r['length'] for r in results]
        successes = [r['success'] for r in results]
        
        metrics = {
            'model': model_name,
            'num_episodes': len(results),
            'avg_reward': np.mean(rewards),
            'std_reward': np.std(rewards),
            'min_reward': np.min(rewards),
            'max_reward': np.max(rewards),
            'avg_length': np.mean(lengths),
            'std_length': np.std(lengths),
            'success_rate': np.mean(successes),
            'success_count': np.sum(successes)
        }
        
        return metrics
    
    def compare_models(self, apf_metrics: Dict, imitation_metrics: Dict, 
                      rl_metrics: Dict) -> None:
        """比较不同模型的性能"""
        print("\n" + "="*80)
        print("MODEL COMPARISON RESULTS")
        print("="*80)
        
        models = [apf_metrics, imitation_metrics, rl_metrics]
        models = [m for m in models if m]  # 过滤空结果
        
        if not models:
            print("No valid results to compare")
            return
        
        # 打印表格
        print(f"{'Model':<12} {'Avg Reward':<12} {'Success Rate':<12} {'Avg Length':<12}")
        print("-" * 50)
        
        for metrics in models:
            if metrics:
                print(f"{metrics['model']:<12} "
                      f"{metrics['avg_reward']:<12.2f} "
                      f"{metrics['success_rate']:<12.2f} "
                      f"{metrics['avg_length']:<12.1f}")
        
        # 绘制比较图
        self._plot_comparison(models)
    
    def _plot_comparison(self, models_metrics: List[Dict]) -> None:
        """绘制模型比较图"""
        if not models_metrics:
            return
            
        model_names = [m['model'] for m in models_metrics]
        avg_rewards = [m['avg_reward'] for m in models_metrics]
        success_rates = [m['success_rate'] for m in models_metrics]
        avg_lengths = [m['avg_length'] for m in models_metrics]
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 平均奖励
        axes[0].bar(model_names, avg_rewards, color=['blue', 'orange', 'green'][:len(model_names)])
        axes[0].set_title('Average Reward')
        axes[0].set_ylabel('Reward')
        axes[0].grid(True, alpha=0.3)
        
        # 成功率
        axes[1].bar(model_names, success_rates, color=['blue', 'orange', 'green'][:len(model_names)])
        axes[1].set_title('Success Rate')
        axes[1].set_ylabel('Success Rate')
        axes[1].set_ylim(0, 1)
        axes[1].grid(True, alpha=0.3)
        
        # 平均回合长度
        axes[2].bar(model_names, avg_lengths, color=['blue', 'orange', 'green'][:len(model_names)])
        axes[2].set_title('Average Episode Length')
        axes[2].set_ylabel('Steps')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"\nComparison plot saved as 'model_comparison.png'")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="USV Model Evaluation")
    parser.add_argument("--episodes", type=int, default=10, help="Number of episodes to evaluate")
    parser.add_argument("--render", action="store_true", help="Render episodes")
    parser.add_argument("--apf", action="store_true", help="Evaluate APF baseline")
    parser.add_argument("--imitation", type=str, help="Path to imitation learning model")
    parser.add_argument("--rl", type=str, help="Path to RL model")
    parser.add_argument("--all", action="store_true", help="Evaluate all available models")
    
    args = parser.parse_args()
    
    evaluator = ModelEvaluator(
        num_episodes=args.episodes,
        max_steps=2000,
        render=args.render
    )
    
    results = {}
    
    # 评估APF基线
    if args.apf or args.all:
        results['apf'] = evaluator.evaluate_apf_baseline()
    
    # 评估模仿学习模型
    imitation_path = args.imitation or "models/imitation_learning/best_model.pt"
    if (args.imitation or args.all) and os.path.exists(imitation_path):
        results['imitation'] = evaluator.evaluate_imitation_model(imitation_path)
    
    # 评估RL模型
    rl_path = args.rl or "models/rl_enhanced/final_model.pt"
    if (args.rl or args.all) and os.path.exists(rl_path):
        results['rl'] = evaluator.evaluate_rl_model(rl_path)
    
    # 比较结果
    if len(results) > 1:
        evaluator.compare_models(
            results.get('apf', {}),
            results.get('imitation', {}),
            results.get('rl', {})
        )
    elif len(results) == 1:
        model_name, metrics = list(results.items())[0]
        print(f"\n{model_name.upper()} Results:")
        for key, value in metrics.items():
            if key != 'model':
                print(f"  {key}: {value}")


if __name__ == "__main__":
    main()