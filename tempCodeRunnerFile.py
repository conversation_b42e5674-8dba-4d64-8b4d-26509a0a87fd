"""
该文件是TD3算法训练的主要文件,包含网络结构 TD3算法 
"""
import os
import math
import time
from collections import deque
from dataclasses import dataclass
from typing import Tuple, Optional, Dict, Any, List
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import random
from usv_rl_env import USVFormationTD3Env 

def fanin_init(tensor: torch.Tensor):
    if tensor is not None:
        size = tensor.size()
        if len(size) == 1:
            bound = 1 / math.sqrt(size[0])
        else:
            bound = 1 / math.sqrt(size[0])
        nn.init.uniform_(tensor, -bound, bound)


class MLP(nn.Module):
    """
    MLP网络结构
    """
    def __init__(self, in_dim: int, out_dim: int, hidden: Tuple[int, int] = (256, 256), out_act: Optional[nn.Module] = None):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(in_dim, hidden[0]), nn.ReLU(),
            nn.Linear(hidden[0], hidden[1]), nn.ReLU(),
            nn.Linear(hidden[1], out_dim)
        )
        self.out_act = out_act
        for m in self.net:
            if isinstance(m, nn.Linear):
                fanin_init(m.weight)
                nn.init.zeros_(m.bias)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        y = self.net(x)
        return self.out_act(y) if self.out_act is not None else y


class Actor(nn.Module):
    """
    Actor 网络结构
    """
    def __init__(self, obs_dim: int, act_dim: int, act_limit: float):
        super().__init__()
        self.mu = MLP(obs_dim, act_dim, out_act=nn.Tanh())
        self.act_limit = act_limit

    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        return self.mu(obs) * self.act_limit


class Critic(nn.Module):
    """
    Critic 网络结构
    """
    def __init__(self, obs_dim: int, act_dim: int):
        super().__init__()
        self.q = MLP(obs_dim + act_dim, 1)

    def forward(self, obs: torch.Tensor, act: torch.Tensor) -> torch.Tensor:
        return self.q(torch.cat([obs, act], dim=-1))


@dataclass
class TD3Config:
    """
    TD3 的算法参配置
    """
    obs_dim: int
    act_dim: int
    act_limit: float = 1.0
    gamma: float = 0.99
    tau: float = 0.005
    policy_noise: float = 0.2
    noise_clip: float = 0.5
    policy_delay: int = 2
    actor_lr: float = 3e-4
    critic_lr: float = 3e-4
    device: str = "cpu"


class ReplayBuffer:
    """
    经验池
    """
    def __init__(self, obs_dim: int, act_dim: int, size: int = 1_000_000):
        self.obs_buf = np.zeros((size, obs_dim), dtype=np.float32)
        self.next_obs_buf = np.zeros((size, obs_dim), dtype=np.float32)
        self.act_buf = np.zeros((size, act_dim), dtype=np.float32)
        self.rew_buf = np.zeros((size,), dtype=np.float32)
        self.done_buf = np.zeros((size,), dtype=np.float32)
        self.max_size = size
        self.ptr = 0
        self.size = 0

    def store(self, obs, act, rew, next_obs, done):
        self.obs_buf[self.ptr] = obs
        self.act_buf[self.ptr] = act
        self.rew_buf[self.ptr] = rew
        self.next_obs_buf[self.ptr] = next_obs
        self.done_buf[self.ptr] = done
        self.ptr = (self.ptr + 1) % self.max_size
        self.size = min(self.size + 1, self.max_size)

    def sample_batch(self, batch_size: int = 256):
        idxs = np.random.randint(0, self.size, size=batch_size)
        return dict(
            obs=self.obs_buf[idxs],
            act=self.act_buf[idxs],
            rew=self.rew_buf[idxs],
            next_obs=self.next_obs_buf[idxs],
            done=self.done_buf[idxs],
        )


class TD3Agent:
    """
    TD3 算法的核心部分
    """
    def __init__(self, cfg: TD3Config):
        self.cfg = cfg
        self.device = torch.device(cfg.device)

        self.actor = Actor(cfg.obs_dim, cfg.act_dim, cfg.act_limit).to(self.device)
        self.actor_target = Actor(cfg.obs_dim, cfg.act_dim, cfg.act_limit).to(self.device)
        self.actor_target.load_state_dict(self.actor.state_dict())

        self.q1 = Critic(cfg.obs_dim, cfg.act_dim).to(self.device)
        self.q2 = Critic(cfg.obs_dim, cfg.act_dim).to(self.device)
        self.q1_target = Critic(cfg.obs_dim, cfg.act_dim).to(self.device)
        self.q2_target = Critic(cfg.obs_dim, cfg.act_dim).to(self.device)
        self.q1_target.load_state_dict(self.q1.state_dict())
        self.q2_target.load_state_dict(self.q2.state_dict())

        self.actor_opt = torch.optim.Adam(self.actor.parameters(), lr=cfg.actor_lr)
        self.q1_opt = torch.optim.Adam(self.q1.parameters(), lr=cfg.critic_lr)
        self.q2_opt = torch.optim.Adam(self.q2.parameters(), lr=cfg.critic_lr)

        self.total_updates = 0

    @torch.no_grad()
    # 动作抽取
    def act(self, obs: np.ndarray, noise_std: float = 0.0) -> np.ndarray:
        obs_t = torch.as_tensor(obs, dtype=torch.float32, device=self.device).unsqueeze(0)
        a = self.actor(obs_t).cpu().numpy()[0]
        if noise_std > 0:
            a = a + np.random.normal(0, noise_std, size=a.shape)
        return np.clip(a, -self.cfg.act_limit, self.cfg.act_limit)

    # 网络更新
    def update(self, replay: ReplayBuffer, batch_size: int = 256):
        batch = replay.sample_batch(batch_size)
        obs = torch.as_tensor(batch['obs'], dtype=torch.float32, device=self.device)
        act = torch.as_tensor(batch['act'], dtype=torch.float32, device=self.device)
        rew = torch.as_tensor(batch['rew'], dtype=torch.float32, device=self.device).unsqueeze(-1)
        next_obs = torch.as_tensor(batch['next_obs'], dtype=torch.float32, device=self.device)
        done = torch.as_tensor(batch['done'], dtype=torch.float32, device=self.device).unsqueeze(-1)
        with torch.no_grad():
            noise = (torch.randn_like(act) * self.cfg.policy_noise).clamp(-self.cfg.noise_clip, self.cfg.noise_clip)
            next_act = (self.actor_target(next_obs) + noise).clamp(-self.cfg.act_limit, self.cfg.act_limit)
            q1_target = self.q1_target(next_obs, next_act)
            q2_target = self.q2_target(next_obs, next_act)
            q_target = torch.min(q1_target, q2_target)
            backup = rew + self.cfg.gamma * (1.0 - done) * q_target

        # Critic loss
        q1_val = self.q1(obs, act)
        q2_val = self.q2(obs, act)
        q1_loss = F.mse_loss(q1_val, backup)
        q2_loss = F.mse_loss(q2_val, backup)
        critic_loss = q1_loss + q2_loss
        self.q1_opt.zero_grad()
        self.q2_opt.zero_grad()
        critic_loss.backward()
        # 添加梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.q1.parameters(), 1.0) 
        torch.nn.utils.clip_grad_norm_(self.q2.parameters(), 1.0) 
        self.q1_opt.step()
        self.q2_opt.step()
        # 这里是 actor_loss 产生大量0的核心原因 !!!
        actor_loss = torch.tensor(0.0, device=self.device)
        # 延迟actor更新   policy_delay = 2
        if self.total_updates % self.cfg.policy_delay == 0:
            actor_loss = -self.q1(obs, self.actor(obs)).mean()
            self.actor_opt.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0) # 添加梯度裁剪
            self.actor_opt.step()
            with torch.no_grad():
                # θ_target = (1 - τ) * θ_target + τ * θ_current
                # τ (tau) 是软更新系数 , θ_target 是目标网络参数 ,θ_current 是当前网络参数
                # Actor目标网络更新
                for p, p_targ in zip(self.actor.parameters(), self.actor_target.parameters()):
                    p_targ.data.mul_(1 - self.cfg.tau)
                    p_targ.data.add_(self.cfg.tau * p.data)
                # Q1目标网络更新
                for p, p_targ in zip(self.q1.parameters(), self.q1_target.parameters()):
                    p_targ.data.mul_(1 - self.cfg.tau)
                    p_targ.data.add_(self.cfg.tau * p.data)
                # Q2目标网络更新
                for p, p_targ in zip(self.q2.parameters(), self.q2_target.parameters()):
                    p_targ.data.mul_(1 - self.cfg.tau)
                    p_targ.data.add_(self.cfg.tau * p.data)
        self.total_updates += 1
        return {
            'critic_loss': float(critic_loss.item()),
            'actor_loss': float(actor_loss.item()),
            'q1': float(q1_val.mean().item()),
            'q2': float(q2_val.mean().item()),
        }

    def save(self, path: str):
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save({
            'actor': self.actor.state_dict(),
            'q1': self.q1.state_dict(),
            'q2': self.q2.state_dict(),
            'actor_t': self.actor_target.state_dict(),
            'q1_t': self.q1_target.state_dict(),
            'q2_t': self.q2_target.state_dict(),
        }, path)

    def load(self, path: str):
        ckpt = torch.load(path, map_location=self.device)
        self.actor.load_state_dict(ckpt['actor'])
        self.q1.load_state_dict(ckpt['q1'])
        self.q2.load_state_dict(ckpt['q2'])
        self.actor_target.load_state_dict(ckpt['actor_t'])
        self.q1_target.load_state_dict(ckpt['q1_t'])
        self.q2_target.load_state_dict(ckpt['q2_t'])


def set_seed(seed: int = 0):
    """
    固定seed 保证实验复现
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

# 画图
def save_plots(log_dir: str,
               steps: List[int],
               ep_returns: List[float],
               success_rates: List[float],
               critic_losses: List[float],
               actor_losses: List[float]): 
    if not steps:
        return
    os.makedirs(log_dir, exist_ok=True)
    plt.figure(figsize=(9, 5))
    plt.plot(steps, ep_returns, label='episode return')
    if len(ep_returns) >= 10:
        # 滑动平均
        k = 10
        mv = np.convolve(ep_returns, np.ones(k)/k, mode='valid')
        plt.plot(steps[k-1:], mv, label='return')
    plt.xlabel('env step')
    plt.ylabel('return')
    plt.legend(); plt.grid(True, alpha=0.3)
    plt.tight_layout(); plt.savefig(os.path.join(log_dir, 'curve_return.png')); plt.close()

    plt.figure(figsize=(9, 5))
    plt.plot(steps, success_rates, label='success rate')
    plt.ylim(-0.05, 1.05)
    plt.xlabel('env step'); plt.ylabel('success rate')
    plt.legend(); plt.grid(True, alpha=0.3)
    plt.tight_layout(); plt.savefig(os.path.join(log_dir, 'curve_success.png')); plt.close()

    if critic_losses:
        plt.figure(figsize=(9, 5))
        plt.plot(steps, critic_losses, label='critic loss')
        plt.plot(steps, actor_losses, label='actor loss')
        plt.xlabel('env step'); plt.ylabel('loss')
        plt.legend(); plt.grid(True, alpha=0.3)
        plt.tight_layout(); plt.savefig(os.path.join(log_dir, 'curve_loss.png')); plt.close()


def run_train(
    total_steps: int = 2_000_000,
    eval_interval: int = 50_000,
    save_interval: int = 50_000,
    log_dir: str = "./logs_custom",
    device: Optional[str] = None,
    seed: int = 0,
    start_steps: int = 10_000,          # 预热随机探索步数
    update_after: int = 10_000,         # 开始更新前所需的经验数量
    update_every: int = 1,              # 每步进行的更新次数
    batch_size: int = 256,              # 批量大小
    eval_episodes: int = 3,             # 评估回合数
    save_align: str = "step",           # 保存对齐方式: "step"(按步数区间, 在回合结束时保存) 或 "episode"
    save_episode_interval: int = 10,    # 按回合对齐时, 每多少回合保存一次
    save_archive: bool = True,          # 是否额外按步数/回合保存唯一文件名快照
    save_max_keep: int = 10,            # 最多保留的快照数量
):
    set_seed(seed)
    env = USVFormationTD3Env(max_time_s=200.0, render_mode=None)
    obs_dim = int(env.get_friend_obs(1).shape[0])
    act_dim = 2
    act_limit = float(env.action_space.high[0])
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    cfg = TD3Config(obs_dim=obs_dim, act_dim=act_dim, act_limit=act_limit, device=device)
    agent = TD3Agent(cfg)
    replay = ReplayBuffer(obs_dim, act_dim, size=1_000_000)

    # 保存统计数据
    os.makedirs(log_dir, exist_ok=True)
    rewards_path = os.path.join(log_dir, "rewards.npy")
    metrics_path = os.path.join(log_dir, "metrics.csv")
    reward_details_path = os.path.join(log_dir, "reward_details.csv")
    model_dir = os.path.join(log_dir, "model")                  # 新增模型保存子目录
    os.makedirs(model_dir, exist_ok=True) 
    model_path = os.path.join(model_dir, "td3_ckpt.pt")         # 更新模型保存路径
    best_model_path = os.path.join(model_dir, "td3_ckpt_best.pt")
    best_eval_return = -float('inf')
    best_eval_success = -float('inf')

    with open(metrics_path, "w", encoding="utf-8") as f:
        f.write("step,ep_return,ep_len,success_rate,critic_loss,actor_loss,q1,q2\n")
    # 保存奖励的细分值
    with open(reward_details_path, "w", encoding="utf-8") as f:
        f.write(
            "step,step_reward,r_form_pos,r_sync,r_safety,r_f2f_safety,r_progress,r_action_cost,stability_bonus,is_success,collision\n"
        )

    _obs_unused, _ = env.reset(seed=seed)
    ep_ret, ep_len, ep_succ = 0.0, 0, 0
    recent_returns = deque(maxlen=100)
    recent_success = deque(maxlen=100)

    plot_steps: List[int] = []
    plot_returns: List[float] = []
    plot_success: List[float] = []
    plot_critic_loss: List[float] = []
    plot_actor_loss: List[float] = []

    print(f"Start training: steps={total_steps}, device={device}, log_dir={log_dir}")
    last_print = time.time()
    # 计数器
    num_episodes_done = 0

    for t in range(1, total_steps + 1):
        # 策略与探索噪声,噪声和训练总步数强相关,也是loss抖动的原因
        noise_std = max(0.005, 0.3 * (1 - t / total_steps))
        obs1 = env.get_friend_obs(1)
        obs2 = env.get_friend_obs(2)
        if t <= start_steps:
            a1 = np.random.uniform(low=-act_limit, high=act_limit, size=act_dim).astype(np.float32)
            a2 = np.random.uniform(low=-act_limit, high=act_limit, size=act_dim).astype(np.float32)
        else:
            a1 = agent.act(obs1, noise_std=noise_std)
            a2 = agent.act(obs2, noise_std=noise_std)
        env_action = np.array([a1[0], a1[1], a2[0], a2[1]], dtype=np.float32)
        next_obs_27, rew, term, trunc, info = env.step(env_action)
        # 步后观测（按友艇）
        next_obs1 = env.get_friend_obs(1)
        next_obs2 = env.get_friend_obs(2)
        # 区分真正终止与时间截断：仅将 terminated 写入 buffer，用于 bootstrap 判定
        done_for_buffer = float(term)
        done = term or trunc
        # 存两条经验，两个友艇共享同一策略
        replay.store(obs1, a1, rew, next_obs1, done_for_buffer)
        replay.store(obs2, a2, rew, next_obs2, done_for_buffer)
        ep_ret += rew
        ep_len += 1

        try:
            with open(reward_details_path, "a", encoding="utf-8") as f:
                f.write(
                    f"{t},{info.get('step_reward', rew)},{info.get('r_form_pos', 0.0)},{info.get('r_sync', 0.0)},{info.get('r_safety', 0.0)},{info.get('r_f2f_safety', 0.0)},{info.get('r_progress', 0.0)},{info.get('r_action_cost', 0.0)},{info.get('stability_bonus', 0.0)},{int(bool(info.get('is_success', False)))},{info.get('collision','')}\n"
                )
        except Exception:
            pass
        metrics = {k: 0.0 for k in ["critic_loss", "actor_loss", "q1", "q2"]}
        if replay.size >= update_after:
            for _ in range(update_every):
                metrics = agent.update(replay, batch_size=batch_size)

        if done:
            recent_returns.append(ep_ret)
            recent_success.append(1.0 if info.get("is_success") else 0.0)
            _obs_unused, _ = env.reset()
            with open(metrics_path, "a", encoding="utf-8") as f:
                f.write(f"{t},{ep_ret},{ep_len},{np.mean(recent_success) if recent_success else 0.0},{metrics['critic_loss']},{metrics['actor_loss']},{metrics['q1']},{metrics['q2']}\n")
            ep_ret, ep_len = 0.0, 0
            plot_steps.append(t)
            plot_returns.append(float(np.mean(recent_returns) if recent_returns else 0.0))
            plot_success.append(float(np.mean(recent_success) if recent_success else 0.0))
            plot_critic_loss.append(metrics['critic_loss'])
            plot_actor_loss.append(metrics['actor_loss'])

            num_episodes_done += 1

            # 阶段性保存(仅当选择按回合对齐时)
            if save_align == "episode":
                if save_episode_interval > 0 and (num_episodes_done % save_episode_interval == 0):
                    agent.save(model_path)
                    np.save(rewards_path, np.array(recent_returns, dtype=np.float32))
                    save_plots(log_dir, plot_steps, plot_returns, plot_success, plot_critic_loss, plot_actor_loss)
                    print(f"Saved checkpoint @ step {t}, episode {num_episodes_done} -> {model_path}")
                    if save_archive:
                        archive_tag = f"epi_{num_episodes_done}_t{t}"
                        archive_path = os.path.join(model_dir, f"td3_ckpt_{archive_tag}.pt")
                        agent.save(archive_path)
                        import glob
                        all_ckpts = glob.glob(os.path.join(model_dir, "td3_ckpt_*.pt"))
                        all_ckpts = [p for p in all_ckpts if not p.endswith("_best.pt") and os.path.basename(p) != "td3_ckpt.pt"]
                        if len(all_ckpts) > save_max_keep:
                            all_ckpts.sort(key=os.path.getmtime)
                            for old_p in all_ckpts[:-save_max_keep]:
                                try:
                                    os.remove(old_p)
                                except Exception:
                                    pass

        # 按固定步数保存
        if save_align == "step" and (t % save_interval == 0):
            agent.save(model_path)
            np.save(rewards_path, np.array(recent_returns, dtype=np.float32))
            save_plots(log_dir, plot_steps, plot_returns, plot_success, plot_critic_loss, plot_actor_loss)
            print(f"Saved checkpoint @ fixed step {t} -> {model_path}")
            if save_archive:
                archive_tag = f"step_{t}"
                archive_path = os.path.join(model_dir, f"td3_ckpt_{archive_tag}.pt")
                agent.save(archive_path)
                import glob
                all_ckpts = glob.glob(os.path.join(model_dir, "td3_ckpt_*.pt"))
                # 排除 best 与 latest
                all_ckpts = [p for p in all_ckpts if not p.endswith("_best.pt") and os.path.basename(p) != "td3_ckpt.pt"]
                if len(all_ckpts) > save_max_keep:
                    all_ckpts.sort(key=os.path.getmtime)
                    for old_p in all_ckpts[:-save_max_keep]:
                        try:
                            os.remove(old_p)
                        except Exception:
                            pass

        # 评估渲染
        if t % eval_interval == 0:
            print(f"Step {t} | avg_return(100)={np.mean(recent_returns) if recent_returns else 0.0:.2f} | success_rate(100)={np.mean(recent_success) if recent_success else 0.0:.2f}")
            # 评估的时候是没有探索噪声的
            eval_env = USVFormationTD3Env(max_time_s=200.0, render_mode=None)
            eval_returns: List[float] = []
            eval_successes: List[float] = []
            for _ in range(eval_episodes):
                _o, _ = eval_env.reset()
                ep_r = 0.0
                while True:
                    # 共享策略两次推理
                    o1 = eval_env.get_friend_obs(1)
                    o2 = eval_env.get_friend_obs(2)
                    aa1 = agent.act(o1, noise_std=0.0)
                    aa2 = agent.act(o2, noise_std=0.0)
                    env_a = np.array([aa1[0], aa1[1], aa2[0], aa2[1]], dtype=np.float32)
                    _o, r, te, tr, inf = eval_env.step(env_a)
                    ep_r += r
                    if te or tr:
                        eval_returns.append(ep_r)
                        eval_successes.append(1.0 if inf.get("is_success") else 0.0)
                        break
            avg_eval_return = float(np.mean(eval_returns)) if eval_returns else -float('inf')
            avg_eval_success = float(np.mean(eval_successes)) if eval_successes else 0.0
            print(f"Eval@{t} | return={avg_eval_return:.2f} | success={avg_eval_success:.2f}")
            improved = False
            if avg_eval_success > best_eval_success + 1e-6:
                improved = True
            elif abs(avg_eval_success - best_eval_success) < 1e-6 and avg_eval_return > best_eval_return + 1e-6:
                improved = True
            if improved:
                best_eval_success = avg_eval_success
                best_eval_return = avg_eval_return
                agent.save(best_model_path)
                print(f"Saved best model to {best_model_path}")
        if time.time() - last_print > 2.0:
            print(f"t={t} replay={replay.size} last_ret={ep_ret:.2f} recent_avg={np.mean(recent_returns) if recent_returns else 0.0:.2f}", flush=True)
            last_print = time.time()
    agent.save(model_path)
    save_plots(log_dir, plot_steps, plot_returns, plot_success, plot_critic_loss, plot_actor_loss)

# 测试训练的模型
def test_model(model_path: str, 
               num_episodes: int = 5, 
               max_time_s: float = 200.0,
               render: bool = True,
               device: str = "cpu"):
    env = USVFormationTD3Env(max_time_s=max_time_s, 
                             render_mode="human" if render else None)
    obs_dim = int(env.get_friend_obs(1).shape[0])
    act_dim = 2
    act_limit = float(env.action_space.high[0])
    
    cfg = TD3Config(obs_dim=obs_dim, act_dim=act_dim, act_limit=act_limit, device=device)
    agent = TD3Agent(cfg)
    
    if not os.path.exists(model_path):
        print(f"错误：模型文件不存在 {model_path}")
        return
    
    try:
        agent.load(model_path)
        print(f"成功加载模型: {model_path}")
    except Exception as e:
        print(f"加载模型失败: {e}")
        return
    
    # 测试统计
    success_count = 0
    total_returns = []
    total_lengths = []
    
    print(f"开始测试 {num_episodes} 个回合...")
    
    for ep in range(num_episodes):
        _o, _ = env.reset()
        ep_return = 0.0
        ep_length = 0
        step_count = 0
        
        print(f"回合 {ep+1}/{num_episodes} 开始")
        
        while True:
            # 共享策略两次推理（无噪声）
            o1 = env.get_friend_obs(1)
            o2 = env.get_friend_obs(2)
            a1 = agent.act(o1, noise_std=0.0)
            a2 = agent.act(o2, noise_std=0.0)
            action4 = np.array([a1[0], a1[1], a2[0], a2[1]], dtype=np.float32)
            _o, reward, terminated, truncated, info = env.step(action4)
            # 渲染模式下按环境步长节流，便于观察
            if render:
                time.sleep(env.dt)
            done = terminated or truncated
            
            ep_return += reward
            ep_length += 1
            step_count += 1
            # 每100步打印一次进度
            if step_count % 100 == 0:
                print(f"  步骤 {step_count}, 累计奖励 {ep_return:.2f}")
            if done:
                success = info.get("is_success", False)
                if success:
                    success_count += 1
                    print(f"  回合 {ep+1} 成功完成！")
                else:
                    print(f"  回合 {ep+1} 失败")
                print(f"  总步数: {ep_length}, 总奖励: {ep_return:.2f}")
                break
        
        total_returns.append(ep_return)
        total_lengths.append(ep_length)
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"成功回合数: {success_count}/{num_episodes}")
    print(f"成功率: {success_count/num_episodes*100:.1f}%")
    print(f"平均回合奖励: {np.mean(total_returns):.2f} ± {np.std(total_returns):.2f}")
    print(f"平均回合长度: {np.mean(total_lengths):.1f} ± {np.std(total_lengths):.1f}")
    print(f"最大回合奖励: {np.max(total_returns):.2f}")
    print(f"最小回合奖励: {np.min(total_returns):.2f}")
    print("="*50)





if __name__ == "__main__":
    MODE = 1                        # 1 ： 'train', 2: 'test'
    TOTAL_STEPS = 10_000_000        # 训练总步数 (仅训练模式) 
    EVAL_INTERVAL = 50_000 
    SAVE_INTERVAL = 50_000 
    SAVE_ALIGN = 'step'             # 'step' 按步数区间(在回合结束时保存) 或 'episode' 按回合数
    SAVE_EPISODE_INTERVAL = 100     # 当 SAVE_ALIGN='episode' 时, 每多少回合保存一次
    SAVE_ARCHIVE = True 
    SAVE_MAX_KEEP = 9999  
    LOG_DIR = './logs_custom' 
    MODEL_PATH = './logs_custom/model/td3_ckpt.pt' 
    NUM_EPISODES = 3 
    MAX_TIME_S = 200.0 
    NO_RENDER = False               
    DEVICE = 'cuda'
    ZERO_STEPS = 1000

    if MODE == 1:
        run_train(total_steps=TOTAL_STEPS,
                  eval_interval=EVAL_INTERVAL,
                  save_interval=SAVE_INTERVAL,
                  log_dir=LOG_DIR,
                  device=DEVICE,
                  seed=0,
                  start_steps=10_000,
                  update_after=10_000,
                  update_every=1,
                  batch_size=256,
                  eval_episodes=3,
                  save_align=SAVE_ALIGN,
                  save_episode_interval=SAVE_EPISODE_INTERVAL,
                  save_archive=SAVE_ARCHIVE,
                  save_max_keep=SAVE_MAX_KEEP)
    elif MODE == 2:
        test_model(model_path=MODEL_PATH,
                   num_episodes=NUM_EPISODES,
                   max_time_s=MAX_TIME_S,
                   render=not NO_RENDER,
                   device=DEVICE)
