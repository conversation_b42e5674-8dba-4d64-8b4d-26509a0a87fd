"""
模仿学习模块 - 从APF轨迹数据学习控制策略
"""
import os
import math
import pickle
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
from dataclasses import dataclass
import csv
from usv_core import TrajectoryStep


def fanin_init(tensor: torch.Tensor):
    """权重初始化"""
    if tensor is not None:
        size = tensor.size()
        if len(size) == 1:
            bound = 1 / math.sqrt(size[0])
        else:
            bound = 1 / math.sqrt(size[0])
        nn.init.uniform_(tensor, -bound, bound)


class ImitationActor(nn.Module):
    """模仿学习Actor网络，与TD3兼容的架构"""
    
    def __init__(self, obs_dim: int, act_dim: int = 2):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(obs_dim, 256), nn.<PERSON>LU(),
            nn.Linear(256, 256), nn.<PERSON>(),
            nn.Linear(256, act_dim), nn.<PERSON>h()
        )
        
        # 使用与TD3相同的权重初始化
        for m in self.net:
            if isinstance(m, nn.Linear):
                fanin_init(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        return self.net(obs)


@dataclass
class ImitationConfig:
    """模仿学习配置"""
    obs_dim: int
    act_dim: int = 2
    learning_rate: float = 1e-3
    batch_size: int = 256
    num_epochs: int = 100
    validation_split: float = 0.2
    device: str = "cpu"
    save_dir: str = "models/imitation_learning"
    log_dir: str = "logs/imitation_learning"
class
 FeatureProcessor:
    """特征处理器 - 将轨迹数据转换为网络输入"""
    
    def __init__(self, map_width: float = 80.0, map_height: float = 50.0):
        self.map_width = map_width
        self.map_height = map_height
        
    def process_trajectory_step(self, step: TrajectoryStep) -> np.ndarray:
        """将轨迹步骤转换为网络输入特征"""
        features = []
        
        # Leader状态 (归一化)
        features.extend([
            step.leader_x / (self.map_width / 2.0),  # 归一化到[-1, 1]
            step.leader_y / (self.map_height / 2.0),
            step.leader_psi / math.pi,  # 归一化到[-1, 1]
            step.leader_u / 0.9,  # 假设最大速度0.9
            step.leader_r / 0.9   # 假设最大角速度0.9
        ])
        
        # 目标信息
        target_dx = step.target_x - step.leader_x
        target_dy = step.target_y - step.leader_y
        features.extend([
            target_dx / (self.map_width / 2.0),
            target_dy / (self.map_height / 2.0),
            step.target_distance / 100.0  # 归一化距离
        ])
        
        # 导航向量
        features.extend([
            step.navigation_vector[0],
            step.navigation_vector[1]
        ])
        
        # 障碍物信息 (取最近的4个障碍物)
        obstacle_features = []
        for i in range(min(4, len(step.obstacle_distances))):
            if i < len(step.obstacle_distances):
                dist = step.obstacle_distances[i] / 50.0  # 归一化距离
                bearing = step.obstacle_bearings[i] / math.pi  # 归一化方位
                obstacle_features.extend([dist, bearing])
            else:
                obstacle_features.extend([1.0, 0.0])  # 填充值
        
        features.extend(obstacle_features)
        
        return np.array(features, dtype=np.float32)
    
    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return 5 + 3 + 2 + 8  # leader(5) + target(3) + nav(2) + obstacles(8)


class TrajectoryDataset(Dataset):
    """轨迹数据集"""
    
    def __init__(self, data_dir: str, feature_processor: FeatureProcessor):
        self.feature_processor = feature_processor
        self.features = []
        self.labels = []
        
        self._load_data(data_dir)
    
    def _load_data(self, data_dir: str):
        """加载轨迹数据"""
        print(f"Loading trajectory data from {data_dir}...")
        
        episode_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl')]
        episode_files.sort()
        
        for episode_file in episode_files:
            episode_path = os.path.join(data_dir, episode_file)
            with open(episode_path, 'rb') as f:
                episode_steps = pickle.load(f)
            
            for step in episode_steps:
                feature = self.feature_processor.process_trajectory_step(step)
                label = np.array([step.thrust_left, step.thrust_right], dtype=np.float32)
                
                self.features.append(feature)
                self.labels.append(label)
        
        self.features = np.array(self.features)
        self.labels = np.array(self.labels)
        
        print(f"Loaded {len(self.features)} trajectory steps")
        print(f"Feature shape: {self.features.shape}")
        print(f"Label shape: {self.labels.shape}")
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.features[idx]), torch.FloatTensor(self.labels[idx])c
lass ImitationLearner:
    """模仿学习训练器"""
    
    def __init__(self, config: ImitationConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建保存目录
        os.makedirs(config.save_dir, exist_ok=True)
        os.makedirs(config.log_dir, exist_ok=True)
        
        # 初始化模型
        self.model = ImitationActor(config.obs_dim, config.act_dim).to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config.learning_rate)
        self.loss_fn = nn.MSELoss()
        
        # 训练记录
        self.train_losses = []
        self.val_losses = []
        self.train_accuracies = []
        self.val_accuracies = []
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        total_samples = 0
        
        for batch_features, batch_labels in train_loader:
            batch_features = batch_features.to(self.device)
            batch_labels = batch_labels.to(self.device)
            
            # 前向传播
            predictions = self.model(batch_features)
            loss = self.loss_fn(predictions, batch_labels)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
            total_loss += loss.item() * batch_features.size(0)
            total_samples += batch_features.size(0)
        
        avg_loss = total_loss / total_samples
        return {"loss": avg_loss}
    
    def evaluate(self, val_loader: DataLoader) -> Dict[str, float]:
        """评估模型性能"""
        self.model.eval()
        total_loss = 0.0
        total_samples = 0
        total_mae = 0.0
        
        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                batch_features = batch_features.to(self.device)
                batch_labels = batch_labels.to(self.device)
                
                predictions = self.model(batch_features)
                loss = self.loss_fn(predictions, batch_labels)
                mae = torch.mean(torch.abs(predictions - batch_labels))
                
                total_loss += loss.item() * batch_features.size(0)
                total_mae += mae.item() * batch_features.size(0)
                total_samples += batch_features.size(0)
        
        avg_loss = total_loss / total_samples
        avg_mae = total_mae / total_samples
        
        return {"loss": avg_loss, "mae": avg_mae}
    
    def train(self, data_dir: str) -> None:
        """完整训练流程"""
        print("Starting imitation learning training...")
        
        # 准备数据
        feature_processor = FeatureProcessor()
        dataset = TrajectoryDataset(data_dir, feature_processor)
        
        # 分割训练和验证集
        val_size = int(len(dataset) * self.config.validation_split)
        train_size = len(dataset) - val_size
        train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
        
        train_loader = DataLoader(train_dataset, batch_size=self.config.batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=self.config.batch_size, shuffle=False)
        
        print(f"Training samples: {train_size}, Validation samples: {val_size}")
        
        # 训练循环
        best_val_loss = float('inf')
        
        for epoch in range(self.config.num_epochs):
            # 训练
            train_metrics = self.train_epoch(train_loader)
            val_metrics = self.evaluate(val_loader)
            
            # 记录指标
            self.train_losses.append(train_metrics["loss"])
            self.val_losses.append(val_metrics["loss"])
            
            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{self.config.num_epochs}")
                print(f"  Train Loss: {train_metrics['loss']:.6f}")
                print(f"  Val Loss: {val_metrics['loss']:.6f}, MAE: {val_metrics['mae']:.6f}")
            
            # 保存最佳模型
            if val_metrics["loss"] < best_val_loss:
                best_val_loss = val_metrics["loss"]
                self.save_model("best_model.pt")
        
        # 保存最终模型和训练记录
        self.save_model("final_model.pt")
        self.save_training_logs()
        self.plot_training_curves()
        
        print("Training completed!")
        print(f"Best validation loss: {best_val_loss:.6f}")
    
    def save_model(self, filename: str) -> None:
        """保存模型"""
        model_path = os.path.join(self.config.save_dir, filename)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config
        }, model_path)
    
    def load_model(self, filename: str) -> None:
        """加载模型"""
        model_path = os.path.join(self.config.save_dir, filename)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    def save_training_logs(self) -> None:
        """保存训练日志"""
        # 保存CSV格式的训练指标
        metrics_path = os.path.join(self.config.log_dir, "training_metrics.csv")
        with open(metrics_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['epoch', 'train_loss', 'val_loss'])
            for i, (train_loss, val_loss) in enumerate(zip(self.train_losses, self.val_losses)):
                writer.writerow([i+1, train_loss, val_loss])
    
    def plot_training_curves(self) -> None:
        """绘制训练曲线"""
        epochs = range(1, len(self.train_losses) + 1)
        
        plt.figure(figsize=(12, 4))
        
        # 损失曲线
        plt.subplot(1, 2, 1)
        plt.plot(epochs, self.train_losses, 'b-', label='Training Loss')
        plt.plot(epochs, self.val_losses, 'r-', label='Validation Loss')
        plt.title('Training and Validation Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 保存图片
        plt.tight_layout()
        plt.savefig(os.path.join(self.config.log_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Training curves saved to {self.config.log_dir}")


def train_imitation_model(data_dir: str, config: Optional[ImitationConfig] = None) -> str:
    """训练模仿学习模型的主函数"""
    if config is None:
        feature_processor = FeatureProcessor()
        obs_dim = feature_processor.get_feature_dim()
        config = ImitationConfig(obs_dim=obs_dim)
    
    learner = ImitationLearner(config)
    learner.train(data_dir)
    
    return config.save_dir


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python imitation_learning.py <trajectory_data_dir> [device]")
        sys.exit(1)
    
    data_dir = sys.argv[1]
    device = sys.argv[2] if len(sys.argv) > 2 else "cpu"
    
    # 配置
    feature_processor = FeatureProcessor()
    obs_dim = feature_processor.get_feature_dim()
    
    config = ImitationConfig(
        obs_dim=obs_dim,
        device=device,
        num_epochs=200,
        batch_size=256,
        learning_rate=1e-3
    )
    
    # 训练
    model_dir = train_imitation_model(data_dir, config)
    print(f"Model saved to: {model_dir}")