"""
增强RL训练模块 - 基于模仿学习的TD3强化学习
"""
import os
import math
import time
import numpy as np
import torch
import torch.nn as nn
from collections import deque
from dataclasses import dataclass
from typing import Optional, Dict, List
import matplotlib.pyplot as plt
import csv

# 导入现有的TD3组件
from train_td3 import TD3Agent, TD3Config, ReplayBuffer, Actor, Critic
from imitation_learning import ImitationActor, FeatureProcessor
from usv_core import USVEnv


@dataclass
class EnhancedRLConfig(TD3Config):
    """增强RL配置，扩展TD3Config"""
    pretrained_model_path: Optional[str] = None
    imitation_warmup_steps: int = 5000  # 使用模仿学习策略的预热步数
    save_dir: str = "models/rl_enhanced"
    log_dir: str = "logs/rl_enhanced"
    
    
class SingleUSVEnv:
    """单艇环境适配器，将USVEnv适配为RL环境"""
    
    def __init__(self, max_time_s: float = 200.0):
        self.env = USVEnv(dt=0.1)
        self.max_steps = int(max_time_s / self.env.dt)
        self.step_count = 0
        self.feature_processor = FeatureProcessor()
        
    def reset(self):
        """重置环境"""
        self.env.reset()
        self.step_count = 0
        return self._get_observation()
    
    def step(self, action: np.ndarray):
        """执行动作"""
        # 将RL动作转换为环境动作
        thrust_left = float(np.clip(action[0], -1.0, 1.0))
        thrust_right = float(np.clip(action[1], -1.0, 1.0))
        
        # 修改环境以接受外部动作而不是APF
        obs, reward, done, info = self._step_with_action((thrust_left, thrust_right))
        
        self.step_count += 1
        if self.step_count >= self.max_steps:
            done = True
            
        return self._get_observation(), reward, done, info
    
    def _step_with_action(self, action):
        """使用给定动作执行步骤"""
        # 直接控制leader而不使用APF
        self.env.leader.step(action, self.env.dt)
        self.env.episode_start_time += self.env.dt
        
        self.env.trails.append((self.env.leader.x, self.env.leader.y))
        if len(self.env.trails) > 1000:
            self.env.trails.pop(0)
            
        obs = self.env._get_obs()
        reward, done, info = self.env._compute_reward_done()
        
        return obs, reward, done, info
    
    def _get_observation(self):
        """获取RL观测"""
        # 构造TrajectoryStep用于特征提取
        lx, ly, lpsi, lu, lr = self.env.leader.get_state()
        gx, gy = self.env.leader_goal
        
        # 计算障碍物信息
        obstacle_distances = []
        obstacle_bearings = []
        for ox, oy in self.env.map.obstacle_centers:
            dist = math.hypot(ox - lx, oy - ly) - self.env.map.obstacle_radius_m
            bearing = math.atan2(oy - ly, ox - lx) - lpsi
            bearing = (bearing + math.pi) % (2 * math.pi) - math.pi
            obstacle_distances.append(dist)
            obstacle_bearings.append(bearing)
        
        # 获取导航向量
        nx, ny, _ = self.env._navigation_vector(lx, ly)
        
        # 创建伪TrajectoryStep
        from usv_core import TrajectoryStep
        step = TrajectoryStep(
            leader_x=lx, leader_y=ly, leader_psi=lpsi, leader_u=lu, leader_r=lr,
            target_x=gx, target_y=gy, target_distance=math.hypot(gx-lx, gy-ly),
            obstacle_distances=obstacle_distances, obstacle_bearings=obstacle_bearings,
            thrust_left=0.0, thrust_right=0.0, timestamp=0.0,
            navigation_vector=(nx, ny)
        )
        
        return self.feature_processor.process_trajectory_step(step)
    
    def render(self):
        """渲染环境"""
        self.env.render()


class EnhancedTD3Agent(TD3Agent):
    """增强TD3智能体，支持从模仿学习初始化"""
    
    def __init__(self, cfg: EnhancedRLConfig):
        super().__init__(cfg)
        self.enhanced_cfg = cfg
        self.imitation_model = None
        
        # 如果提供了预训练模型路径，加载模仿学习权重
        if cfg.pretrained_model_path and os.path.exists(cfg.pretrained_model_path):
            self.load_imitation_weights(cfg.pretrained_model_path)
            print(f"Loaded imitation learning weights from {cfg.pretrained_model_path}")
    
    def load_imitation_weights(self, model_path: str) -> None:
        """从模仿学习模型加载权重到actor网络"""
        checkpoint = torch.load(model_path, map_location=self.device)
        imitation_state_dict = checkpoint['model_state_dict']
        
        # 将模仿学习的权重复制到TD3 actor
        self.actor.load_state_dict(imitation_state_dict)
        self.actor_target.load_state_dict(imitation_state_dict)
        
        print("Successfully loaded imitation learning weights into TD3 actor")
    
    def act_with_imitation_fallback(self, obs: np.ndarray, noise_std: float = 0.0, 
                                   use_imitation: bool = False) -> np.ndarray:
        """带模仿学习回退的动作选择"""
        if use_imitation and self.imitation_model is not None:
            # 使用模仿学习模型
            obs_t = torch.as_tensor(obs, dtype=torch.float32, device=self.device).unsqueeze(0)
            with torch.no_grad():
                action = self.imitation_model(obs_t).cpu().numpy()[0]
        else:
            # 使用TD3策略
            action = self.act(obs, noise_std)
        
        return actionclass 

class EnhancedTrainer:
    """增强训练器"""
    
    def __init__(self, config: EnhancedRLConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建保存目录
        os.makedirs(config.save_dir, exist_ok=True)
        os.makedirs(config.log_dir, exist_ok=True)
        
        # 初始化环境和智能体
        self.env = SingleUSVEnv(max_time_s=200.0)
        self.agent = EnhancedTD3Agent(config)
        
        # 初始化经验池
        obs_dim = self.env.feature_processor.get_feature_dim()
        self.replay_buffer = ReplayBuffer(obs_dim, config.act_dim, size=1_000_000)
        
        # 训练记录
        self.episode_returns = []
        self.episode_lengths = []
        self.success_rates = []
        self.training_losses = []
        self.steps_done = 0
        self.episodes_done = 0
        
    def enhanced_training_loop(self, total_steps: int, eval_interval: int = 10000, 
                             save_interval: int = 50000) -> None:
        """增强训练循环"""
        print(f"Starting enhanced RL training for {total_steps} steps...")
        print(f"Device: {self.device}")
        print(f"Pretrained model: {self.config.pretrained_model_path}")
        
        # 初始化
        obs = self.env.reset()
        episode_return = 0.0
        episode_length = 0
        recent_returns = deque(maxlen=100)
        recent_successes = deque(maxlen=100)
        
        start_time = time.time()
        last_eval_time = time.time()
        
        for step in range(1, total_steps + 1):
            self.steps_done = step
            
            # 选择动作
            if step <= self.config.start_steps:
                # 随机探索
                action = np.random.uniform(-1.0, 1.0, size=self.config.act_dim)
            elif step <= self.config.imitation_warmup_steps:
                # 使用模仿学习策略（如果可用）
                noise_std = 0.1
                action = self.agent.act(obs, noise_std)
            else:
                # 正常TD3策略
                noise_std = max(0.01, 0.2 * (1 - step / total_steps))
                action = self.agent.act(obs, noise_std)
            
            # 执行动作
            next_obs, reward, done, info = self.env.step(action)
            
            # 存储经验
            self.replay_buffer.store(obs, action, reward, next_obs, float(done))
            
            # 更新状态
            obs = next_obs
            episode_return += reward
            episode_length += 1
            
            # 训练智能体
            if self.replay_buffer.size >= self.config.update_after and step % self.config.update_every == 0:
                for _ in range(self.config.update_every):
                    batch = self.replay_buffer.sample_batch(self.config.batch_size)
                    loss_info = self.agent.update(self.replay_buffer, self.config.batch_size)
                    self.training_losses.append(loss_info)
            
            # 回合结束处理
            if done:
                recent_returns.append(episode_return)
                recent_successes.append(1.0 if info.get('success', False) else 0.0)
                
                self.episode_returns.append(episode_return)
                self.episode_lengths.append(episode_length)
                self.episodes_done += 1
                
                # 重置环境
                obs = self.env.reset()
                episode_return = 0.0
                episode_length = 0
            
            # 定期评估和保存
            if step % eval_interval == 0:
                avg_return = np.mean(recent_returns) if recent_returns else 0.0
                success_rate = np.mean(recent_successes) if recent_successes else 0.0
                
                elapsed_time = time.time() - last_eval_time
                steps_per_sec = eval_interval / elapsed_time
                
                print(f"Step {step}/{total_steps}")
                print(f"  Avg Return (100): {avg_return:.2f}")
                print(f"  Success Rate (100): {success_rate:.2f}")
                print(f"  Episodes: {self.episodes_done}")
                print(f"  Steps/sec: {steps_per_sec:.1f}")
                print(f"  Replay Buffer: {self.replay_buffer.size}")
                
                self.success_rates.append(success_rate)
                last_eval_time = time.time()
            
            # 定期保存
            if step % save_interval == 0:
                self.save_checkpoint(f"checkpoint_step_{step}.pt")
                self.save_training_logs()
                self.plot_training_progress()
                print(f"Saved checkpoint at step {step}")
        
        # 训练结束，保存最终结果
        self.save_checkpoint("final_model.pt")
        self.save_training_logs()
        self.plot_training_progress()
        
        total_time = time.time() - start_time
        print(f"Training completed in {total_time:.1f} seconds")
        print(f"Final average return: {np.mean(recent_returns) if recent_returns else 0.0:.2f}")
        print(f"Final success rate: {np.mean(recent_successes) if recent_successes else 0.0:.2f}")
    
    def save_checkpoint(self, filename: str) -> None:
        """保存检查点"""
        checkpoint_path = os.path.join(self.config.save_dir, filename)
        self.agent.save(checkpoint_path)
        
        # 保存训练状态
        training_state = {
            'steps_done': self.steps_done,
            'episodes_done': self.episodes_done,
            'episode_returns': self.episode_returns,
            'episode_lengths': self.episode_lengths,
            'success_rates': self.success_rates,
            'config': self.config
        }
        
        state_path = os.path.join(self.config.save_dir, f"training_state_{filename}")
        torch.save(training_state, state_path)
    
    def save_training_logs(self) -> None:
        """保存训练日志"""
        # 保存回合数据
        episodes_path = os.path.join(self.config.log_dir, "episodes.csv")
        with open(episodes_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['episode', 'return', 'length'])
            for i, (ret, length) in enumerate(zip(self.episode_returns, self.episode_lengths)):
                writer.writerow([i+1, ret, length])
        
        # 保存成功率数据
        if self.success_rates:
            success_path = os.path.join(self.config.log_dir, "success_rates.csv")
            with open(success_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['eval_step', 'success_rate'])
                for i, rate in enumerate(self.success_rates):
                    writer.writerow([(i+1) * 10000, rate])  # 假设每10000步评估一次
        
        # 保存损失数据
        if self.training_losses:
            loss_path = os.path.join(self.config.log_dir, "training_losses.csv")
            with open(loss_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['step', 'critic_loss', 'actor_loss', 'q1', 'q2'])
                for i, loss_info in enumerate(self.training_losses):
                    writer.writerow([i+1, loss_info.get('critic_loss', 0), 
                                   loss_info.get('actor_loss', 0),
                                   loss_info.get('q1', 0), loss_info.get('q2', 0)])
    
    def plot_training_progress(self) -> None:
        """绘制训练进度"""
        if not self.episode_returns:
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 回合奖励
        axes[0, 0].plot(self.episode_returns)
        if len(self.episode_returns) >= 10:
            # 滑动平均
            window = min(50, len(self.episode_returns) // 10)
            moving_avg = np.convolve(self.episode_returns, np.ones(window)/window, mode='valid')
            axes[0, 0].plot(range(window-1, len(self.episode_returns)), moving_avg, 'r-', linewidth=2)
        axes[0, 0].set_title('Episode Returns')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Return')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 回合长度
        axes[0, 1].plot(self.episode_lengths)
        axes[0, 1].set_title('Episode Lengths')
        axes[0, 1].set_xlabel('Episode')
        axes[0, 1].set_ylabel('Steps')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 成功率
        if self.success_rates:
            eval_steps = [i * 10000 for i in range(1, len(self.success_rates) + 1)]
            axes[1, 0].plot(eval_steps, self.success_rates, 'g-', linewidth=2)
            axes[1, 0].set_title('Success Rate')
            axes[1, 0].set_xlabel('Training Steps')
            axes[1, 0].set_ylabel('Success Rate')
            axes[1, 0].set_ylim(0, 1)
            axes[1, 0].grid(True, alpha=0.3)
        
        # 训练损失
        if self.training_losses:
            critic_losses = [loss.get('critic_loss', 0) for loss in self.training_losses[-1000:]]  # 最近1000步
            actor_losses = [loss.get('actor_loss', 0) for loss in self.training_losses[-1000:]]
            
            axes[1, 1].plot(critic_losses, label='Critic Loss')
            axes[1, 1].plot(actor_losses, label='Actor Loss')
            axes[1, 1].set_title('Training Losses (Recent 1000 updates)')
            axes[1, 1].set_xlabel('Update Step')
            axes[1, 1].set_ylabel('Loss')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.config.log_dir, 'training_progress.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close()


def train_enhanced_rl(pretrained_model_path: str, total_steps: int = 1_000_000,
                     device: str = "cpu") -> str:
    """训练增强RL模型的主函数"""
    
    # 配置
    feature_processor = FeatureProcessor()
    obs_dim = feature_processor.get_feature_dim()
    
    config = EnhancedRLConfig(
        obs_dim=obs_dim,
        act_dim=2,
        act_limit=1.0,
        device=device,
        pretrained_model_path=pretrained_model_path,
        start_steps=5000,
        update_after=5000,
        imitation_warmup_steps=10000,
        batch_size=256,
        save_dir="models/rl_enhanced",
        log_dir="logs/rl_enhanced"
    )
    
    # 训练
    trainer = EnhancedTrainer(config)
    trainer.enhanced_training_loop(
        total_steps=total_steps,
        eval_interval=10000,
        save_interval=50000
    )
    
    return config.save_dir


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python rl_enhanced_training.py <pretrained_model_path> [total_steps] [device]")
        sys.exit(1)
    
    pretrained_path = sys.argv[1]
    total_steps = int(sys.argv[2]) if len(sys.argv) > 2 else 1_000_000
    device = sys.argv[3] if len(sys.argv) > 3 else "cpu"
    
    if not os.path.exists(pretrained_path):
        print(f"Error: Pretrained model not found at {pretrained_path}")
        sys.exit(1)
    
    print(f"Starting enhanced RL training...")
    print(f"Pretrained model: {pretrained_path}")
    print(f"Total steps: {total_steps}")
    print(f"Device: {device}")
    
    model_dir = train_enhanced_rl(pretrained_path, total_steps, device)
    print(f"Enhanced RL model saved to: {model_dir}")