import math
from typing import Tuple, Optional, Dict, Any
import numpy as np
import gymnasium as gym
from gymnasium.spaces import Box
from usv_core import USVEnv

# 仿真决策的时间 s
TICK_TIME = 1       

def wrap_pi(angle: float) -> float:
    return (angle + math.pi) % (2.0 * math.pi) - math.pi


class USVFormationTD3Env(gym.Env):
    """
    原始环境封装为RL环境  包含奖励设计 状态空间 动作空间
    """
    metadata = {"render_modes": ["human"], "render_fps": 30}

    def __init__(self, max_time_s: float = 200.0, render_mode: Optional[str] = None):
        super().__init__()
        self.env = USVEnv(dt=TICK_TIME)
        self.dt = self.env.dt
        self.max_steps = int(max_time_s / self.dt)
        self.render_mode = render_mode
        self.action_space = Box(low=-1.0, high=1.0, shape=(4,), dtype=np.float32)
        # 观测扩展为 31 维：在原有 27 维基础上新增4维残差特征
        # - 4 维：两友艇相对其“编队虚拟点”的速度误差（Leader 坐标系下）
        # - 2 维：友艇间相对位置向量（Leader 坐标系下）
        # - 1 维：Leader→Goal 的距离（归一化）
        # - 4 维：基于 p0,p1,p2 的残差归一化向量 [v1/(||v1||+||v2||+eps), v2/(||v1||+||v2||+eps)]
        self.observation_space = Box(low=-np.inf, high=np.inf, shape=(31,), dtype=np.float32)

        # Episode state
        self.step_count = 0
        self.prev_action = np.zeros(4, dtype=np.float32)
        self.prev_leader_goal_dist = 0.0
        # 编队稳定保持计数
        self._formation_hold_steps = 0

    def reset(self, *, seed: Optional[int] = None, options: Optional[Dict[str, Any]] = None):
        super().reset(seed=seed)
        self.env.reset()  # 固定起点/障碍物，不做随机
        self.step_count = 0
        self.prev_action[:] = 0.0
        self.prev_leader_goal_dist = self._leader_goal_distance()
        self._formation_hold_steps = 0
        obs = self._get_obs()
        if self.render_mode == "human":
            self.env.render()
        return obs, {}

    def step(self, action: np.ndarray):
        action = np.asarray(action, dtype=np.float32)
        action = np.clip(action, -1.0, 1.0)

        obs, _, _, _ = self.env.step((float(action[0]), float(action[1]), float(action[2]), float(action[3])))

        # Compute reward and done based on designed shaping
        reward, terminated, truncated, info = self._compute_reward_done(action)
        self.step_count += 1

        if self.render_mode == "human":
            self.env.render()

        return self._get_obs(), reward, terminated, truncated, info

    def render(self):
        self.env.render()

    def close(self):
        pass

    def _rot_to_leader_frame(self, x: float, y: float, psi_l: float) -> Tuple[float, float]:
        c, s = math.cos(-psi_l), math.sin(-psi_l)
        return c * x - s * y, s * x + c * y

    def _leader_goal_distance(self) -> float:
        (lx, ly, *_), (gx, gy) = self.env.leader.get_state(), self.env.leader_goal
        return math.hypot(gx - lx, gy - ly)

    def _get_triangle_vertices(self, side_len: float):
        lx, ly, lpsi, *_ = self.env.leader.get_state()
        return self.env._triangle_vertices((lx, ly), side_len, lpsi)

    def _closest_obstacle_distance(self, x: float, y: float) -> float:
        d_min = 1e9
        radius = self.env.map.obstacle_radius_m
        for (ox, oy) in self.env.map.obstacle_centers:
            d = math.hypot(x - ox, y - oy) - radius
            if d < d_min:
                d_min = d
        return d_min

    def _distance_to_boundary(self, x: float, y: float) -> float:
        half_w = self.env.map.width_m / 2.0
        half_h = self.env.map.height_m / 2.0
        return min(half_w - abs(x), half_h - abs(y))

    def _side_length(self) -> float:
        # 与 usv_env._compute_reward_done 一致的动态收缩逻辑
        shrink = -self.env._nearest_obstacle_penalty(self.env.leader.x, self.env.leader.y) * 0.1 # Corrected from self.env.y
        # 为避免在障碍附近把编队边长缩得过小，设置更高的最小值
        return max(8.0, 10.0 - shrink)

    def _get_obs(self) -> np.ndarray:
        """
        单模型控2船的obs设计(一个模型一次输出2个船的动作) 暂未用
        """
        # Leader state
        lx, ly, lpsi, lu, lr = self.env.leader.get_state()
        # 导航向量（单位）
        nx, ny, _ = self.env._navigation_vector(lx, ly)

        # 目标顶点与编队虚拟点（随 leader 运动）
        side_len = self._side_length()
        p1, p2 = self.env._triangle_vertices((lx, ly), side_len, lpsi)

        # Friend1/2
        x1, y1, psi1, u1, r1 = self.env.friend1.get_state()
        x2, y2, psi2, u2, r2 = self.env.friend2.get_state()

        # 位置向量（世界坐标）与期望
        # v01 = p0 - p1 , v21 = p2 - p1
        v01x, v01y = (lx - x1), (ly - y1)
        v21x, v21y = (x2 - x1), (y2 - y1)
        # 期望：vd01 = p0 - p1* , vd21 = p2* - p1*
        vd01x, vd01y = (lx - p1[0]), (ly - p1[1])
        vd21x, vd21y = (p2[0] - p1[0]), (p2[1] - p1[1])
        # 残差
        rv1x, rv1y = (vd01x - v01x), (vd01y - v01y)
        rv2x, rv2y = (vd21x - v21x), (vd21y - v21y)
        denom = (math.hypot(rv1x, rv1y) + math.hypot(rv2x, rv2y) + 1e-6)
        nrv1x, nrv1y = rv1x / denom, rv1y / denom
        nrv2x, nrv2y = rv2x / denom, rv2y / denom

        # 相对位置误差（Leader 坐标系）
        e1x_world, e1y_world = x1 - p1[0], y1 - p1[1]
        e2x_world, e2y_world = x2 - p2[0], y2 - p2[1]
        e1x, e1y = self._rot_to_leader_frame(e1x_world, e1y_world, lpsi)
        e2x, e2y = self._rot_to_leader_frame(e2x_world, e2y_world, lpsi)

        # 朝向与速度误差（相对 leader）
        dpsi1 = wrap_pi(psi1 - lpsi)
        dpsi2 = wrap_pi(psi2 - lpsi)
        du1 = (u1 - lu) / 0.9
        du2 = (u2 - lu) / 0.9
        dr1 = (r1 - lr) / 0.9
        dr2 = (r2 - lr) / 0.9

        # 安全相关
        d_obs1 = self._closest_obstacle_distance(x1, y1)
        d_obs2 = self._closest_obstacle_distance(x2, y2)
        d_bound1 = self._distance_to_boundary(x1, y1)
        d_bound2 = self._distance_to_boundary(x2, y2)

        # Leader → Goal 向量（Leader 坐标系）与距离
        gx, gy = self.env.leader_goal
        gvecx, gvecy = self._rot_to_leader_frame(gx - lx, gy - ly, lpsi)
        D_now = math.hypot(gx - lx, gy - ly)

        # 编队虚拟点速度（世界坐标）与友艇速度，在 Leader 坐标系下的相对速度误差
        h = math.sqrt(3) / 2.0 * side_len
        offset = side_len / 2.0
        # 虚拟点在 leader 体坐标中的相对位置
        r1_body = (-h, +offset)
        r2_body = (-h, -offset)
        c, s = math.cos(lpsi), math.sin(lpsi)
        # 体到世界旋转
        def rot_to_world(rx: float, ry: float) -> Tuple[float, float]:
            return (c * rx - s * ry, s * rx + c * ry)
        r1_world = rot_to_world(*r1_body)
        r2_world = rot_to_world(*r2_body)
        # Leader 线速度（世界）
        vLx, vLy = lu * c, lu * s
        # 刚体旋转引起的点速度分量 ω × r = r * [-r_y, r_x]
        vp1x, vp1y = vLx + lr * (-r1_world[1]), vLy + lr * (r1_world[0])
        vp2x, vp2y = vLx + lr * (-r2_world[1]), vLy + lr * (r2_world[0])
        # 友艇线速度（世界）
        vf1x, vf1y = u1 * math.cos(psi1), u1 * math.sin(psi1)
        vf2x, vf2y = u2 * math.cos(psi2), u2 * math.sin(psi2)
        # 相对速度（世界）
        dv1x_w, dv1y_w = (vf1x - vp1x), (vf1y - vp1y)
        dv2x_w, dv2y_w = (vf2x - vp2x), (vf2y - vp2y)
        # 转到 Leader 坐标系
        dv1x, dv1y = self._rot_to_leader_frame(dv1x_w, dv1y_w, lpsi)
        dv2x, dv2y = self._rot_to_leader_frame(dv2x_w, dv2y_w, lpsi)

        # 友艇间相对位置（Leader 坐标系）
        f2f_x_w, f2f_y_w = (x2 - x1), (y2 - y1)
        f2f_x, f2f_y = self._rot_to_leader_frame(f2f_x_w, f2f_y_w, lpsi)

        # 归一化/裁剪（大致到 [-1,1]）
        def clip01(v, s):
            return float(np.clip(v / s, -1.0, 1.0))

        obs = np.array([
            # Leader 基本状态与导航方向
            clip01(lu, 0.9), clip01(lr, 0.9),
            nx, ny,
            # Friend1 编队误差与同步
            clip01(e1x, 40.0), clip01(e1y, 40.0), dpsi1 / math.pi, clip01(du1, 1.0), clip01(dr1, 1.0), clip01(d_obs1, 20.0), clip01(d_bound1, 20.0),
            # Friend2 编队误差与同步
            clip01(e2x, 40.0), clip01(e2y, 40.0), dpsi2 / math.pi, clip01(du2, 1.0), clip01(dr2, 1.0), clip01(d_obs2, 20.0), clip01(d_bound2, 20.0),
            # Leader→Goal 向量（Leader 坐标系）
            clip01(gvecx, 80.0), clip01(gvecy, 80.0),
            # 新增：相对编队虚拟点的速度误差（Leader 坐标系）
            clip01(dv1x, 1.0), clip01(dv1y, 1.0), clip01(dv2x, 1.0), clip01(dv2y, 1.0),
            # 新增：友艇间的相对位置（Leader 坐标系）
            clip01(f2f_x, 40.0), clip01(f2f_y, 40.0),
            # 新增：Leader→Goal 的距离
            clip01(D_now, 160.0),
            # 新增：基于 p0,p1,p2 的4维残差归一化特征（世界坐标）
            clip01(nrv1x, 1.0), clip01(nrv1y, 1.0), clip01(nrv2x, 1.0), clip01(nrv2y, 1.0),
        ], dtype=np.float32)
        return obs

    def get_friend_obs(self, friend: int) -> np.ndarray:
        """
        单模型控单船的obs   目前使用的方案
        (2个船就是同一个模型走2次)
        """
        lx, ly, lpsi, lu, lr = self.env.leader.get_state()
        nx, ny, _ = self.env._navigation_vector(lx, ly)
        side_len = self._side_length()
        p1, p2 = self.env._triangle_vertices((lx, ly), side_len, lpsi)
        x1, y1, psi1, u1, r1 = self.env.friend1.get_state()
        x2, y2, psi2, u2, r2 = self.env.friend2.get_state()

        # 残差归一化特征（世界坐标，与主观测一致）
        v01x, v01y = (lx - x1), (ly - y1)
        v21x, v21y = (x2 - x1), (y2 - y1)
        vd01x, vd01y = (lx - p1[0]), (ly - p1[1])
        vd21x, vd21y = (p2[0] - p1[0]), (p2[1] - p1[1])
        rv1x, rv1y = (vd01x - v01x), (vd01y - v01y)
        rv2x, rv2y = (vd21x - v21x), (vd21y - v21y)
        denom = (math.hypot(rv1x, rv1y) + math.hypot(rv2x, rv2y) + 1e-6)
        nrv1x, nrv1y = rv1x / denom, rv1y / denom
        nrv2x, nrv2y = rv2x / denom, rv2y / denom

        # 友艇选择
        if friend == 1:
            xt, yt, psit, ut, rt = x1, y1, psi1, u1, r1
            px, py = p1
        elif friend == 2:
            xt, yt, psit, ut, rt = x2, y2, psi2, u2, r2
            px, py = p2
        else:
            raise ValueError("friend must be 1 or 2")

        # 相对误差（leader 坐标系）
        ex_w, ey_w = xt - px, yt - py
        ex, ey = self._rot_to_leader_frame(ex_w, ey_w, lpsi)
        dpsi = wrap_pi(psit - lpsi)
        du = (ut - lu) / 0.9
        dr = (rt - lr) / 0.9

        # 安全项
        d_obs = self._closest_obstacle_distance(xt, yt)
        d_bound = self._distance_to_boundary(xt, yt)

        # 目标向量与距离
        gx, gy = self.env.leader_goal
        gvecx, gvecy = self._rot_to_leader_frame(gx - lx, gy - ly, lpsi)
        D_now = math.hypot(gx - lx, gy - ly)

        # 编队虚拟点速度误差（leader 坐标系）
        h = math.sqrt(3) / 2.0 * side_len
        offset = side_len / 2.0
        r_body = (-h, +offset) if friend == 1 else (-h, -offset)
        c, s = math.cos(lpsi), math.sin(lpsi)
        r_world = (c * r_body[0] - s * r_body[1], s * r_body[0] + c * r_body[1])
        vLx, vLy = lu * c, lu * s
        vpx, vpy = vLx + lr * (-r_world[1]), vLy + lr * (r_world[0])
        vtx, vty = ut * math.cos(psit), ut * math.sin(psit)
        dvx_w, dvy_w = (vtx - vpx), (vty - vpy)
        dvx, dvy = self._rot_to_leader_frame(dvx_w, dvy_w, lpsi)

        # 友艇间相对位置（leader 坐标系）
        f2f_x_w, f2f_y_w = (x2 - x1), (y2 - y1)
        f2f_x, f2f_y = self._rot_to_leader_frame(f2f_x_w, f2f_y_w, lpsi)

        def clip01(v, s):
            return float(np.clip(v / s, -1.0, 1.0))

        obs = np.array([
            clip01(lu, 0.9), clip01(lr, 0.9),                                                       # 2
            nx, ny,                                                                                 # 4
            clip01(ex, 40.0), clip01(ey, 40.0), dpsi / math.pi, clip01(du, 1.0), clip01(dr, 1.0),   # 9
            clip01(d_obs, 20.0), clip01(d_bound, 20.0),                                             # 11
            clip01(gvecx, 80.0), clip01(gvecy, 80.0),                                               # 13
            clip01(dvx, 1.0), clip01(dvy, 1.0),                                                     # 15
            clip01(f2f_x, 40.0), clip01(f2f_y, 40.0),                                               # 17
            clip01(D_now, 160.0),                                                                   # 18
            clip01(nrv1x, 1.0), clip01(nrv1y, 1.0), clip01(nrv2x, 1.0), clip01(nrv2y, 1.0),         # 22
        ], dtype=np.float32)
        return obs


    def _compute_reward_done(self, action: np.ndarray):
        """
        奖励设计部分
        """
        # --- 1. 定义奖励与惩罚的权重和常数 ---
        # 终局奖励
        R_SUCCESS = 500.0  # 成功完成任务的巨大奖励
        R_FAILURE_CRASH = -500.0  # 碰撞或出界/友艇互撞的巨大惩罚

        # 步进塑形奖励的权重
        W_FORM_POS = 3.0      # 编队位置奖励权重 (正向) 加强，促使学编队
        W_FORM_SYNC = 0.5     # 航向与速度同步惩罚权重
        W_PROGRESS = 2.0      # 领航艇朝向目标前进的奖励权重
        W_ACTION_COST = 0.01  # 动作能量消耗惩罚权重
        W_SAFETY = 2.0        # 接近障碍物/边界的惩罚权重 下调，避免压制编队
        W_F2F_SAFETY = 3.0    # 友艇之间接近的惩罚权重 适度下调
        W_FORM_STABILITY_BONUS = 2.5  # 编队稳定保持的逐步加成

        # 关键阈值
        FORMATION_ERROR_THRESHOLD = 15.0  # 编队误差大于此值时，屏蔽前进奖励
        SUCCESS_RADIUS = 10.0 # 领航艇距离终点的成功半径
        SUCCESS_FORMATION_ERROR = 5.0 # 成功时要求的最大编队误差
        SAFETY_MARGIN = 6.0 # 安全边界，进入此范围开始有惩罚
        # 舰艇碰撞与安全间隔（单位体积碰撞）：设定舰艇半径 1m
        SHIP_RADIUS = 1.0
        SHIP_COLLISION_DIST = 2.0 * SHIP_RADIUS  # 中心距离<=2m 视为碰撞
        FRIEND_SAFE_MARGIN = 3.0       # 安全距离（m），进入此范围开始持续惩罚
        # 编队判稳门限与保持时长
        FORMATION_TOL = 4.0
        FORMATION_STABILITY_STEPS = 8


        # --- 2. 计算当前状态的误差和距离 ---
        lx, ly, lpsi, lu, _ = self.env.leader.get_state()
        side_len = self._side_length()
        p1, p2 = self.env._triangle_vertices((lx, ly), side_len, lpsi)
        x1, y1, psi1, u1, _ = self.env.friend1.get_state()
        x2, y2, psi2, u2, _ = self.env.friend2.get_state()
        # 友艇间距离
        d_f2f = math.hypot(x2 - x1, y2 - y1)

        # 位置误差
        e_pos1 = math.hypot(x1 - p1[0], y1 - p1[1])
        e_pos2 = math.hypot(x2 - p2[0], y2 - p2[1])
        
        # 同步误差
        e_psi1_abs = abs(wrap_pi(psi1 - lpsi))
        e_psi2_abs = abs(wrap_pi(psi2 - lpsi))
        e_u1_abs = abs(u1 - lu)
        e_u2_abs = abs(u2 - lu)

        # 安全距离
        d_obsL = self._closest_obstacle_distance(lx, ly)
        d_obs1 = self._closest_obstacle_distance(x1, y1)
        d_obs2 = self._closest_obstacle_distance(x2, y2)
        d_bound1 = self._distance_to_boundary(x1, y1)
        d_bound2 = self._distance_to_boundary(x2, y2)


        # --- 3. 计算各项奖励/惩罚 ---

        # (A) 编队保持奖励 (正向激励)
        # 使用指数函数，误差越小，奖励越高，且接近0时奖励最高。
        r_formation_pos = math.exp(-0.2 * e_pos1) + math.exp(-0.2 * e_pos2)  # 范围 [0, 2]

        # (B) 状态同步惩罚
        # 对航向和速度的误差进行惩罚
        r_sync = -((e_psi1_abs / math.pi) + (e_psi2_abs / math.pi) + (e_u1_abs / 0.9) + (e_u2_abs / 0.9))

        # (C) 安全惩罚
        # 当智能体进入安全边界内时，给予二次方增长的惩罚，越近越罚得重
        penalty_obs = max(0, SAFETY_MARGIN - d_obs1)**2 + max(0, SAFETY_MARGIN - d_obs2)**2
        penalty_bound = max(0, SAFETY_MARGIN - d_bound1)**2 + max(0, SAFETY_MARGIN - d_bound2)**2
        penalty_f2f = max(0, FRIEND_SAFE_MARGIN - d_f2f)**2
        r_safety = -(penalty_obs + penalty_bound)
        r_f2f_safety = -penalty_f2f

        # (D) 任务进度奖励 (Gated)
        # 只有当编队基本形成时，才对领航艇的前进给予奖励
        D_now = self._leader_goal_distance()
        r_progress = 0
        if e_pos1 < FORMATION_ERROR_THRESHOLD and e_pos2 < FORMATION_ERROR_THRESHOLD:
            r_progress = self.prev_leader_goal_dist - D_now
        self.prev_leader_goal_dist = D_now

        # (E) 动作消耗惩罚
        r_action_cost = -np.sum(np.square(action))
        
        # --- 4. 组合成总的步进奖励 ---
        step_reward = (
            W_FORM_POS * r_formation_pos +
            W_FORM_SYNC * r_sync +
            W_SAFETY * r_safety +
            W_F2F_SAFETY * r_f2f_safety +
            W_PROGRESS * r_progress +
            W_ACTION_COST * r_action_cost
        )

        # (F) 编队稳定保持逐步加成（不终止回合）
        if e_pos1 <= FORMATION_TOL and e_pos2 <= FORMATION_TOL:
            self._formation_hold_steps += 1
        else:
            self._formation_hold_steps = 0
        stability_factor = min(1.0, self._formation_hold_steps / FORMATION_STABILITY_STEPS)
        step_reward += W_FORM_STABILITY_BONUS * stability_factor
        
        # 更新上一步动作，用于计算动作变化率（平滑度）惩罚，此项可选
        self.prev_action = action.copy()


        # --- 5. 判断回合是否终止/截断 ---
        terminated = False
        truncated = False
        # 回传奖励组成，便于训练侧记录
        info = {
            "is_success": False,
            "formation_hold_steps": self._formation_hold_steps,
            "r_form_pos": float(r_formation_pos),
            "r_sync": float(r_sync),
            "r_safety": float(r_safety),
            "r_f2f_safety": float(r_f2f_safety),
            "r_progress": float(r_progress),
            "r_action_cost": float(r_action_cost),
            "stability_bonus": float(W_FORM_STABILITY_BONUS * stability_factor),
            "step_reward": float(step_reward),
        }

        # 终止条件 1: 成功抵达
        if D_now <= SUCCESS_RADIUS:
            terminated = True
            # 抵达时必须保持良好队形才算成功
            if e_pos1 <= SUCCESS_FORMATION_ERROR and e_pos2 <= SUCCESS_FORMATION_ERROR:
                info["is_success"] = True
                step_reward += R_SUCCESS  # 给予巨大成功奖励
            else:
                # 抵达但队形散乱，算失败
                step_reward += R_FAILURE_CRASH / 2 # 给予较大失败惩罚

        # 碰撞判定：
        # 1) 障碍物：触碰到障碍物半径外的 1m 即为碰撞 → clearance <= 1.0
        obstacle_collision = (d_obsL <= 1.0) or (d_obs1 <= 1.0) or (d_obs2 <= 1.0)
        # 2) 舰艇-舰艇：半径均为 1m，中心距离<=2m 即碰撞
        d_LF1 = math.hypot(x1 - lx, y1 - ly)
        d_LF2 = math.hypot(x2 - lx, y2 - ly)
        friend_collision = (d_f2f <= SHIP_COLLISION_DIST) or (d_LF1 <= SHIP_COLLISION_DIST) or (d_LF2 <= SHIP_COLLISION_DIST)
        if (obstacle_collision or d_bound1 <= 0 or d_bound2 <= 0 or friend_collision):
            if not terminated: # 避免重复惩罚
                terminated = True
                step_reward += R_FAILURE_CRASH # 给予巨大失败惩罚
                if friend_collision:
                    if d_f2f <= SHIP_COLLISION_DIST:
                        info["collision"] = "friend_friend"
                    elif d_LF1 <= SHIP_COLLISION_DIST:
                        info["collision"] = "leader_friend1"
                    elif d_LF2 <= SHIP_COLLISION_DIST:
                        info["collision"] = "leader_friend2"
                elif obstacle_collision:
                    # 标记最先触碰的对象（近似）
                    if d_obsL <= 1.0:
                        info["collision"] = "leader_obstacle"
                    elif d_obs1 <= 1.0:
                        info["collision"] = "friend1_obstacle"
                    elif d_obs2 <= 1.0:
                        info["collision"] = "friend2_obstacle"

        # 截断条件: 时间耗尽
        if self.step_count + 1 >= self.max_steps:
            truncated = True
            if not info["is_success"]:
                # 如果不是因为成功而结束，超时也算一种小失败
                step_reward -= 50.0

        # 保证返回的组成与最终奖励一致
        info["step_reward"] = float(step_reward)

        return step_reward, terminated, truncated, info