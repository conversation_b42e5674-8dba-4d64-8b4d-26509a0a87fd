# Design Document

## Overview

This design outlines a comprehensive USV reinforcement learning system that transforms the existing multi-agent formation control environment into a single-agent learning system. The system follows a three-phase approach: APF data collection, imitation learning, and reinforcement learning enhancement using TD3.

The design leverages the existing USV simulation infrastructure while implementing significant modifications to support the learning pipeline. The system will generate diverse training scenarios through randomized environments and collect expert demonstrations from APF control for supervised learning.

## Architecture

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   APF Data      │    │   Imitation     │    │   RL Training   │
│   Collection    │───▶│   Learning      │───▶│   (TD3)         │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Trajectory Data │    │ Trained IL      │    │ Enhanced RL     │
│ (Episodes)      │    │ Model           │    │ Model           │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Modules

1. **Modified USV Environment** (`usv_core.py`)
   - Single leader vessel with APF control
   - 80x80m environment with randomized start/end positions
   - Trajectory data collection system

2. **Imitation Learning Module** (`imitation_learning.py`)
   - Neural network with TD3-compatible architecture
   - Supervised learning from APF demonstrations
   - Comprehensive logging and metrics tracking

3. **Enhanced RL Training** (`rl_enhanced_training.py`)
   - TD3 algorithm with imitation learning initialization
   - Progressive improvement beyond expert demonstrations
   - Model and data preservation system

## Components and Interfaces

### 1. Environment Modifications (usv_core.py)

#### Core Changes
- **Single Vessel Operation**: Remove `friend1` and `friend2` vessels, retain only `leader`
- **Map Scaling**: Reduce from 160x160m to 80x80m
- **Randomized Positioning**: 
  - Start positions: Right side of map (x ∈ [30, 40], y ∈ [-30, 30])
  - End positions: Left side of map (x ∈ [-40, -30], y ∈ [-30, 30])
  - Minimum 10m separation between start and end points

#### Trajectory Collection Interface
```python
class TrajectoryCollector:
    def __init__(self, save_episodes: int = 1000):
        self.save_episodes = save_episodes
        self.current_episode = 0
        self.episode_data = []
        
    def collect_step(self, state_info: dict, apf_action: tuple) -> None:
        """Collect single timestep data"""
        
    def save_episode(self) -> None:
        """Save completed episode data"""
        
    def is_collection_complete(self) -> bool:
        """Check if target episodes collected"""
```

#### Data Structure
```python
@dataclass
class TrajectoryStep:
    # Leader state
    leader_x: float
    leader_y: float
    leader_psi: float
    leader_u: float
    leader_r: float
    
    # Target information
    target_x: float
    target_y: float
    target_distance: float
    
    # Obstacle information (relative to leader)
    obstacle_distances: List[float]
    obstacle_bearings: List[float]
    
    # APF control output (labels)
    thrust_left: float
    thrust_right: float
    
    # Additional context
    timestamp: float
    navigation_vector: Tuple[float, float]
```

### 2. Imitation Learning Module

#### Network Architecture
The imitation learning network mirrors the TD3 actor architecture to ensure compatibility:

```python
class ImitationActor(nn.Module):
    def __init__(self, obs_dim: int, act_dim: int = 2):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(obs_dim, 256), nn.ReLU(),
            nn.Linear(256, 256), nn.ReLU(),
            nn.Linear(256, act_dim), nn.Tanh()
        )
        
    def forward(self, obs: torch.Tensor) -> torch.Tensor:
        return self.net(obs)
```

#### Feature Processing Pipeline
```python
class FeatureProcessor:
    def process_trajectory_step(self, step: TrajectoryStep) -> np.ndarray:
        """Convert trajectory step to network input features"""
        # Normalize positions relative to map bounds
        # Encode obstacle information as distance/bearing pairs
        # Include target direction and distance
        # Add leader velocity and orientation
        
    def normalize_features(self, features: np.ndarray) -> np.ndarray:
        """Apply consistent normalization"""
```

#### Training Interface
```python
class ImitationLearner:
    def __init__(self, obs_dim: int, learning_rate: float = 1e-3):
        self.model = ImitationActor(obs_dim)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate)
        self.loss_fn = nn.MSELoss()
        
    def train_epoch(self, dataloader) -> Dict[str, float]:
        """Train for one epoch, return metrics"""
        
    def evaluate(self, test_loader) -> Dict[str, float]:
        """Evaluate model performance"""
        
    def save_model(self, path: str) -> None:
        """Save trained model"""
```

### 3. Enhanced RL Training Module

#### TD3 Integration
```python
class EnhancedTD3Agent(TD3Agent):
    def __init__(self, cfg: TD3Config, pretrained_path: Optional[str] = None):
        super().__init__(cfg)
        if pretrained_path:
            self.load_imitation_weights(pretrained_path)
            
    def load_imitation_weights(self, path: str) -> None:
        """Load imitation learning weights into actor network"""
        
    def enhanced_training_loop(self, env, total_steps: int) -> None:
        """Training loop with comprehensive logging"""
```

## Data Models

### Trajectory Data Storage
```
data/
├── apf_trajectories/
│   ├── episode_0001.pkl
│   ├── episode_0002.pkl
│   └── ...
├── processed_features/
│   ├── train_features.npy
│   ├── train_labels.npy
│   ├── val_features.npy
│   └── val_labels.npy
└── metadata.json
```

### Model Storage Structure
```
models/
├── imitation_learning/
│   ├── checkpoints/
│   ├── final_model.pt
│   └── training_logs/
└── rl_enhanced/
    ├── checkpoints/
    ├── final_model.pt
    └── training_logs/
```

### Metrics and Logging Structure
```
logs/
├── imitation_learning/
│   ├── loss_curves.png
│   ├── training_metrics.csv
│   ├── validation_metrics.csv
│   └── model_performance.json
└── rl_enhanced/
    ├── reward_curves.png
    ├── training_metrics.csv
    ├── episode_logs.csv
    └── model_checkpoints/
```

## Error Handling

### Data Collection Phase
- **Simulation Failures**: Implement episode restart mechanism
- **Data Corruption**: Validate trajectory data before saving
- **Storage Issues**: Check disk space and permissions before collection

### Imitation Learning Phase
- **Training Instability**: Implement gradient clipping and learning rate scheduling
- **Overfitting**: Use validation split and early stopping
- **Memory Issues**: Implement batch processing for large datasets

### RL Enhancement Phase
- **Initialization Failures**: Fallback to random initialization if pretrained model fails
- **Training Divergence**: Implement training stability monitoring
- **Resource Management**: Monitor GPU memory and implement checkpointing

## Testing Strategy

### Unit Testing
- **Environment Modifications**: Test single vessel operation and randomization
- **Data Collection**: Verify trajectory data integrity and format
- **Feature Processing**: Test normalization and feature extraction
- **Model Architecture**: Verify network compatibility between phases

### Integration Testing
- **End-to-End Pipeline**: Test complete workflow from data collection to RL training
- **Model Transfer**: Verify successful weight transfer between learning phases
- **Performance Validation**: Compare imitation learning performance against APF baseline

### Performance Testing
- **Data Collection Speed**: Measure trajectory collection throughput
- **Training Efficiency**: Monitor training time and convergence rates
- **Memory Usage**: Profile memory consumption during training
- **Model Inference**: Test real-time performance of trained models

### Validation Testing
- **APF Reproduction**: Verify imitation learning can reproduce APF behavior
- **RL Improvement**: Confirm RL training improves beyond imitation baseline
- **Generalization**: Test performance on unseen environment configurations