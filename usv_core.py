"""
仿真训练环境核心
"""
import math
import random
import pickle
import os
import csv
from dataclasses import dataclass
from typing import List, Tuple, Optional
import pygame

def clamp(value: float, min_value: float, max_value: float) -> float:
    if value < min_value:
        return min_value
    if value > max_value:
        return max_value
    return value


@dataclass
class SimpleUSVParams:
    thrust_max_newton: float = 52.407
    lever_arm_m: float = 1.2
    mass_longitudinal_kg: float = 189.0   
    mass_sway_kg: float = 1240.0          
    yaw_inertia_kgm2: float = 250.0      
    du_linear: float = 51.3
    du_quad: float = 72.4
    dr_linear: float = 96.8
    u_abs_max: float = 0.9
    r_abs_max: float = 0.9
    thrust_eff_surge_pos: float = 1.0
    thrust_eff_surge_neg: float = 1.0
    thrust_eff_yaw_pos: float = 1.0
    thrust_eff_yaw_neg: float = 0.355


class SimpleUSV:
    def __init__(self, params: Optional[SimpleUSVParams] = None):
        self.p = params or SimpleUSVParams()
        # State
        self.x = 0.0
        self.y = 0.0
        self.psi = 0.0
        self.u = 0.0
        self.r = 0.0

    def get_state(self) -> Tuple[float, float, float, float, float]:
        return (self.x, self.y, self.psi, self.u, self.r)

    def set_state(self, state: Tuple[float, float, float, float, float]) -> None:
        self.x, self.y, self.psi, self.u, self.r = state
    def step(self, action: Tuple[float, float], dt: float) -> Tuple[float, float, float, float, float]:
        Tl, Tr = action
        # 1) Saturate actions
        Tl = clamp(Tl, -1.0, 1.0)
        Tr = clamp(Tr, -1.0, 1.0)

        Tl_surge = (self.p.thrust_eff_surge_pos * Tl) if Tl >= 0.0 else (self.p.thrust_eff_surge_neg * Tl)
        Tr_surge = (self.p.thrust_eff_surge_pos * Tr) if Tr >= 0.0 else (self.p.thrust_eff_surge_neg * Tr)
        Tl_yaw = (self.p.thrust_eff_yaw_pos * Tl) if Tl >= 0.0 else (self.p.thrust_eff_yaw_neg * Tl)
        Tr_yaw = (self.p.thrust_eff_yaw_pos * Tr) if Tr >= 0.0 else (self.p.thrust_eff_yaw_neg * Tr)

        tau_x = (Tl_surge + Tr_surge) * self.p.thrust_max_newton
        tau_z = (Tr_yaw - Tl_yaw) * self.p.lever_arm_m * self.p.thrust_max_newton

        du = (
            tau_x
            - self.p.du_linear * self.u
            - self.p.du_quad * abs(self.u) * self.u
        ) / self.p.mass_longitudinal_kg
        dr = (tau_z - self.p.dr_linear * self.r) / self.p.yaw_inertia_kgm2

        self.u += du * dt
        self.r += dr * dt


        self.u = clamp(self.u, -self.p.u_abs_max, self.p.u_abs_max)
        self.r = clamp(self.r, -self.p.r_abs_max, self.p.r_abs_max)

        self.psi += self.r * dt
        self.x += self.u * math.cos(self.psi) * dt
        self.y += self.u * math.sin(self.psi) * dt

        return self.get_state()



@dataclass
class MapConfig:
    width_m: float = 80.0
    height_m: float = 50.0
    origin_x: float = 0.0
    origin_y: float = 0.0

    obstacle_centers: Tuple[Tuple[float, float], ...] = (
        (0.0, 5.0),
        (20.0, 0.0),
        (0.0, -10.0),
        (-20.0, 0.0),
        (15.0, 12.0),
        (-15.0, 12.0),
    )
    obstacle_radius_m: float = 4.0


@dataclass
class RenderConfig:
    screen_px: int = 800
    bg_color: Tuple[int, int, int] = (245, 248, 250)
    map_color: Tuple[int, int, int] = (30, 30, 30)
    axis_color: Tuple[int, int, int] = (120, 120, 120)
    tick_color: Tuple[int, int, int] = (150, 150, 150)
    obstacle_color: Tuple[int, int, int] = (200, 80, 80)
    leader_color: Tuple[int, int, int] = (60, 120, 240)
    friend_color: Tuple[int, int, int] = (40, 170, 90)
    trail_color: Tuple[int, int, int] = (160, 160, 160)


@dataclass
class TrajectoryStep:
    """单个时间步的轨迹数据（保存原始信息）"""
    # Leader状态（原始）
    leader_x: float
    leader_y: float
    leader_psi: float
    leader_u: float
    leader_r: float
    
    # 目标信息（原始）
    target_x: float
    target_y: float
    
    # 障碍物信息（原始世界坐标 + 统一半径）
    obstacle_centers: List[Tuple[float, float]]
    obstacle_radius: float
    
    # APF控制输出（可选作为标签）
    thrust_left: float
    thrust_right: float
    
    # 时间戳
    timestamp: float


class TrajectoryCollector:
    """轨迹数据收集器"""
    
    def __init__(self, max_episodes: int = 1000):
        self.max_episodes = max_episodes
        self.current_episode = 0
        self.episode_data = []
        self.current_episode_steps = []
        
        # 创建数据保存目录
        self.data_dir = "data/apf_trajectories"
        os.makedirs(self.data_dir, exist_ok=True)
        
    def collect_step(self, state_info: dict, apf_action: Tuple[float, float], timestamp: float) -> None:
        """收集单个时间步数据"""
        step = TrajectoryStep(
            leader_x=state_info['leader_x'],
            leader_y=state_info['leader_y'],
            leader_psi=state_info['leader_psi'],
            leader_u=state_info['leader_u'],
            leader_r=state_info['leader_r'],
            target_x=state_info['target_x'],
            target_y=state_info['target_y'],
            obstacle_centers=state_info['obstacle_centers'],
            obstacle_radius=state_info['obstacle_radius'],
            thrust_left=apf_action[0],
            thrust_right=apf_action[1],
            timestamp=timestamp,
        )
        self.current_episode_steps.append(step)
        
    def save_episode(self) -> None:
        """保存完成的回合数据为 CSV 文件"""
        if not self.current_episode_steps:
            return

        episode_file = os.path.join(self.data_dir, f"episode_{self.current_episode:04d}.csv")

        # 固定障碍物数量（由地图定义），不足则补空，超出则截断
        max_obs = 0
        for step in self.current_episode_steps:
            if isinstance(step.obstacle_distances, list):
                max_obs = max(max_obs, len(step.obstacle_distances))
        # 若未能推断，取地图默认的 6 个
        if max_obs == 0:
            max_obs = 6

        # 生成表头（仅原始信息）
        header = [
            "timestamp",
            "leader_x", "leader_y", "leader_psi", "leader_u", "leader_r",
            "target_x", "target_y",
            "thrust_left", "thrust_right",
            "obstacle_radius",
        ]
        for i in range(max_obs):
            header.append(f"obstacle_ox_{i}")
        for i in range(max_obs):
            header.append(f"obstacle_oy_{i}")

        # 写入 CSV
        with open(episode_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(header)
            for s in self.current_episode_steps:
                row = [
                    s.timestamp,
                    s.leader_x, s.leader_y, s.leader_psi, s.leader_u, s.leader_r,
                    s.target_x, s.target_y,
                    s.thrust_left, s.thrust_right,
                    s.obstacle_radius,
                ]
                # 障碍物中心：补齐或截断
                centers = list(s.obstacle_centers) if isinstance(s.obstacle_centers, list) else []
                if len(centers) < max_obs:
                    centers = centers + [("", "")] * (max_obs - len(centers))
                else:
                    centers = centers[:max_obs]
                # 按列展开 ox_* 与 oy_*
                row.extend([c[0] for c in centers])
                row.extend([c[1] for c in centers])
                writer.writerow(row)

        print(f"Saved episode {self.current_episode} to CSV with {len(self.current_episode_steps)} steps")
        self.current_episode += 1
        self.current_episode_steps = []
            
    def is_collection_complete(self) -> bool:
        """检查是否完成目标回合数的收集"""
        return self.current_episode >= self.max_episodes


class USVEnv:
    """
    当前使用的仿真推演环境 - 单艇版本
    """

    def __init__(self, dt: float = 0.1, map_cfg: Optional[MapConfig] = None, params: Optional[SimpleUSVParams] = None, render_cfg: Optional[RenderConfig] = None, collect_trajectories: bool = False, max_episodes: int = 1000, max_episode_steps: int = 2000):
        self.dt = dt
        self.map = map_cfg or MapConfig()
        self.params = params or SimpleUSVParams()
        self.render_cfg = render_cfg or RenderConfig()
        self.clock = None
        
        # 轨迹收集相关
        self.collect_trajectories = collect_trajectories
        self.max_episodes = max_episodes
        self.trajectory_collector = None
        if collect_trajectories:
            self.trajectory_collector = TrajectoryCollector(max_episodes)

        # 回合时长控制（防卡死）
        self.max_episode_steps = max_episode_steps
        self.episode_steps = 0

        # 只保留leader船只
        self.leader = SimpleUSV(self.params)

        # Rendering
        self.pygame_initialized = False
        self.screen = None
        self.font = None
        self.scale = self.render_cfg.screen_px / max(self.map.width_m, self.map.height_m)

        # 随机化起点和终点将在reset中实现
        self.leader_start = None
        self.leader_goal = None

        self.reset()


    def _world_to_screen(self, x: float, y: float) -> Tuple[int, int]:
        half_px = self.render_cfg.screen_px / 2.0
        sx = int(half_px + x * self.scale)
        sy = int(half_px - y * self.scale)
        return sx, sy

    def _meters_to_px(self, m: float) -> int:
        return int(m * self.scale)

    def reset(self):
        # 随机化起点和终点
        self._randomize_start_end_positions()
        
        self.leader.set_state((*self.leader_start, 0.0, 0.0))
        self.trails: List[Tuple[float, float]] = []
        self.episode_start_time = 0.0
        self.episode_steps = 0
        return self._get_obs()
    
    def _randomize_start_end_positions(self):
        """随机化起点和终点位置"""
        half_h = self.map.height_m / 2.0  # 25m
        
        # 起点在地图右侧
        start_x = random.uniform(25.0, self.map.width_m / 2.0 - 5.0)  # 25到35m
        start_y = random.uniform(-half_h + 5.0, half_h - 5.0)  # -20到20m
        start_psi = random.uniform(math.pi * 0.5, math.pi * 1.5)  # 朝向大致向左
        
        # 终点在地图左侧，与起点保持至少10m距离
        while True:
            end_x = random.uniform(-self.map.width_m / 2.0 + 5.0, -25.0)  # -35到-25m
            end_y = random.uniform(-half_h + 5.0, half_h - 5.0)  # -20到20m
            
            # 检查距离
            if math.hypot(end_x - start_x, end_y - start_y) >= 10.0:
                break
        
        self.leader_start = (start_x, start_y, start_psi)
        self.leader_goal = (end_x, end_y)

    def _script_leader_action(self) -> Tuple[float, float]:
        """
        基于APF的leader控制
        """
        x, y, psi, u, r = self.leader.get_state()
        # self._navigation_vector(x, y) APF部分
        nx, ny, rep_norm = self._navigation_vector(x, y)
        desired_heading = math.atan2(ny, nx)
        heading_error = (desired_heading - psi + math.pi) % (2 * math.pi) - math.pi
        yaw_cmd = clamp(heading_error, -1.0, 1.0)
        gx, gy = self.leader_goal
        dist_goal = max(0.0, math.hypot(gx - x, gy - y))
        base_speed = 0.9 * (1.0 - 1.0 / (1.0 + 0.15 * dist_goal))  # 0→远处增速，近终点减速
        speed_cmd = clamp(base_speed * (1.0 / (1.0 + 0.8 * rep_norm)), 0.25, 0.9)
        T_l = clamp(speed_cmd - 0.5 * yaw_cmd, -1.0, 1.0)
        T_r = clamp(speed_cmd + 0.5 * yaw_cmd, -1.0, 1.0)
        return (T_l, T_r)

    def _navigation_vector(self, x: float, y: float) -> Tuple[float, float, float]:
        gx, gy = self.leader_goal
        dx, dy = gx - x, gy - y
        dist_goal = math.hypot(dx, dy) + 1e-6
        att = (dx / dist_goal, dy / dist_goal)

        R_inf = 20.0 
        safety = self.map.obstacle_radius_m + 2.0
        k_rep = 4.0
        k_tan = 2.0

        rep_x = 0.0
        rep_y = 0.0
        tan_x = 0.0
        tan_y = 0.0
        for (ox, oy) in self.map.obstacle_centers:
            vx = x - ox
            vy = y - oy
            d = math.hypot(vx, vy)
            if d <= 1e-6:
                continue
            clearance = d - safety
            if clearance < R_inf:

                mag = k_rep * max(0.0, (R_inf - clearance)) / (R_inf * max(clearance, 1.0))
                rx = (vx / d) * mag
                ry = (vy / d) * mag
                rep_x += rx
                rep_y += ry
                goal_dir = att
                tx1, ty1 = -ry, rx  # +90 deg
                tx2, ty2 = ry, -rx  # -90 deg
                dot1 = tx1 * goal_dir[0] + ty1 * goal_dir[1]
                dot2 = tx2 * goal_dir[0] + ty2 * goal_dir[1]
                if dot1 > dot2:
                    tan_x += k_tan * tx1
                    tan_y += k_tan * ty1
                else:
                    tan_x += k_tan * tx2
                    tan_y += k_tan * ty2

        half_w = self.map.width_m / 2.0
        half_h = self.map.height_m / 2.0
        Rb = 20.0

        left = x - (-half_w)
        if left < Rb:
            rep_x += k_rep * max(0.0, (Rb - left)) / Rb

        right = (half_w) - x
        if right < Rb:
            rep_x -= k_rep * max(0.0, (Rb - right)) / Rb

        bottom = y - (-half_h)
        if bottom < Rb:
            rep_y += k_rep * max(0.0, (Rb - bottom)) / Rb

        top = (half_h) - y
        if top < Rb:
            rep_y -= k_rep * max(0.0, (Rb - top)) / Rb

        vx = att[0] + rep_x + tan_x
        vy = att[1] + rep_y + tan_y
        norm = math.hypot(vx, vy) + 1e-6
        nx = vx / norm
        ny = vy / norm
        rep_norm = math.hypot(rep_x, rep_y)
        return nx, ny, rep_norm

    def step(self, action: Optional[Tuple[float, float]] = None):
        """
        推演函数 - 单艇版本
        """
        # 使用APF控制
        TlL, TrL = self._script_leader_action()
        
        # 如果启用轨迹收集，记录数据
        if self.collect_trajectories and self.trajectory_collector:
            state_info = self._get_state_info_for_collection()
            self.trajectory_collector.collect_step(state_info, (TlL, TrL), self.episode_start_time)
        
        self.leader.step((TlL, TrL), self.dt)
        self.episode_start_time += self.dt
        self.episode_steps += 1

        self.trails.append((self.leader.x, self.leader.y))
        if len(self.trails) > 1000:
            self.trails.pop(0)

        obs = self._get_obs()
        reward, done, info = self._compute_reward_done()

        # 达到最大步数，强制结束（避免难例卡死）
        if not done and self.episode_steps >= self.max_episode_steps:
            done = True
            info = dict(info)
            info["timeout"] = True
        
        # 如果回合结束且启用轨迹收集，保存回合数据
        if done and self.collect_trajectories and self.trajectory_collector:
            self.trajectory_collector.save_episode()
            
        return obs, reward, done, info
    
    def _get_state_info_for_collection(self) -> dict:
        """获取用于轨迹收集的状态信息"""
        lx, ly, lpsi, lu, lr = self.leader.get_state()
        gx, gy = self.leader_goal
        # 原始障碍物世界坐标与统一半径
        obstacle_centers = list(self.map.obstacle_centers)
        obstacle_radius = float(self.map.obstacle_radius_m)

        return {
            'leader_x': lx,
            'leader_y': ly,
            'leader_psi': lpsi,
            'leader_u': lu,
            'leader_r': lr,
            'target_x': gx,
            'target_y': gy,
            'obstacle_centers': obstacle_centers,
            'obstacle_radius': obstacle_radius,
        }



    def _distance(self, a: Tuple[float, float], b: Tuple[float, float]) -> float:
        return math.hypot(a[0] - b[0], a[1] - b[1])

    def _nearest_obstacle_penalty(self, x: float, y: float) -> float:
        d_min = 1e9
        for (ox, oy) in self.map.obstacle_centers:
            d = self._distance((x, y), (ox, oy)) - self.map.obstacle_radius_m
            if d < d_min:
                d_min = d
        if d_min < 0:
            return -5.0
        if d_min < 6.0:
            return - (6.0 - d_min) * 0.5
        return 0.0

    def _compute_reward_done(self):
        """单艇版本的奖励计算"""
        lx, ly, lpsi, lu, lr = self.leader.get_state()
        gx, gy = self.leader_goal
        
        # 距离目标的奖励
        dist_to_goal = math.hypot(gx - lx, gy - ly)
        goal_reward = -0.1 * dist_to_goal
        
        # 障碍物惩罚
        obs_penalty = self._nearest_obstacle_penalty(lx, ly)
        
        # 速度平滑惩罚
        smooth_penalty = -0.01 * (abs(lu) + abs(lr))
        
        reward = goal_reward + obs_penalty + smooth_penalty
        
        # 检查是否完成任务
        success = dist_to_goal < 5.0  # 5米内算成功
        
        # 检查是否出界或碰撞
        bound = max(self.map.width_m, self.map.height_m) / 2.0
        out_of_bounds = abs(lx) > bound or abs(ly) > bound
        collision = self._nearest_obstacle_penalty(lx, ly) < -4.0  # 碰撞阈值
        
        done = success or out_of_bounds or collision
        
        if success:
            reward += 100.0  # 成功奖励
        elif collision:
            reward -= 100.0  # 碰撞惩罚
        elif out_of_bounds:
            reward -= 50.0   # 出界惩罚
            
        info = {
            "success": success,
            "collision": collision,
            "out_of_bounds": out_of_bounds,
            "distance_to_goal": dist_to_goal,
        }
        return reward, done, info

    def _get_obs(self):
        """单艇版本的观测"""
        lx, ly, lpsi, lu, lr = self.leader.get_state()
        gx, gy = self.leader_goal
        goal_vec = (gx - lx, gy - ly)
        
        # 获取导航向量
        nx, ny, _ = self._navigation_vector(lx, ly)
        
        return (
            lx, ly, lpsi, lu, lr,
            goal_vec[0], goal_vec[1],
            nx, ny,
        )

    def render(self):
        if not self.pygame_initialized:
            pygame.init()
            self.screen = pygame.display.set_mode((self.render_cfg.screen_px, self.render_cfg.screen_px))
            pygame.display.set_caption("USV Formation Env")
            self.font = pygame.font.SysFont("consolas", 16)
            self.pygame_initialized = True
            self.clock = pygame.time.Clock()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.display.quit()
                self.pygame_initialized = False
                return

        self.screen.fill(self.render_cfg.bg_color)

        border_rect = pygame.Rect(
            self._world_to_screen(-self.map.width_m / 2, self.map.height_m / 2),
            (self._meters_to_px(self.map.width_m), self._meters_to_px(self.map.height_m)),
        )
        pygame.draw.rect(self.screen, self.render_cfg.map_color, border_rect, 2)

        self._draw_text("Boundary: 80m x 50m", (-38, 22))
        self._draw_axes()

        for (ox, oy) in self.map.obstacle_centers:
            pygame.draw.circle(
                self.screen,
                self.render_cfg.obstacle_color,
                self._world_to_screen(ox, oy),
                self._meters_to_px(self.map.obstacle_radius_m),
                2,
            )

        if self.leader_start:
            self._draw_marker(self.leader_start[0], self.leader_start[1], (20, 90, 200), "S")
        if self.leader_goal:
            self._draw_marker(self.leader_goal[0], self.leader_goal[1], (240, 120, 30), "G")

        for i in range(1, len(self.trails)):
            p0 = self._world_to_screen(*self.trails[i - 1])
            p1 = self._world_to_screen(*self.trails[i])
            pygame.draw.line(self.screen, self.render_cfg.trail_color, p0, p1, 1)

        self._draw_boat(self.leader, self.render_cfg.leader_color)

        pygame.display.flip()
        if self.clock is not None and self.dt > 0:
            self.clock.tick(int(max(1, round(1.0 / self.dt))))

    def _draw_boat(self, boat: SimpleUSV, color: Tuple[int, int, int]):
        x, y = boat.x, boat.y
        psi = boat.psi
        length = 4.0  # m
        width = 1.6
        pts = [
            (length / 2, 0),
            (-length / 2, width / 2),
            (-length / 2, -width / 2),
        ]
        world_pts = []
        for px, py in pts:
            wx = x + px * math.cos(psi) - py * math.sin(psi)
            wy = y + px * math.sin(psi) + py * math.cos(psi)
            world_pts.append(self._world_to_screen(wx, wy))
        pygame.draw.polygon(self.screen, color, world_pts, 0)

    def _draw_marker(self, x: float, y: float, color: Tuple[int, int, int], label: str):
        pos = self._world_to_screen(x, y)
        pygame.draw.circle(self.screen, color, pos, max(4, self._meters_to_px(0.6)))
        pygame.draw.circle(self.screen, (0, 0, 0), pos, max(4, self._meters_to_px(0.6)), 2)
        if self.font:
            text = self.font.render(label, True, (10, 10, 10))
            self.screen.blit(text, (pos[0] + 6, pos[1] - 18))

    def _draw_text(self, text: str, world_pos: Tuple[float, float]):
        if not self.font:
            return
        surf = self.font.render(text, True, (50, 50, 50))
        self.screen.blit(surf, self._world_to_screen(world_pos[0], world_pos[1]))

    def _draw_axes(self):
        half_w = self.map.width_m / 2.0
        half_h = self.map.height_m / 2.0
        pygame.draw.line(
            self.screen,
            self.render_cfg.axis_color,
            self._world_to_screen(-half_w, 0.0),
            self._world_to_screen(half_w, 0.0),
            1,
        )
        # Y axis (x=0)
        pygame.draw.line(
            self.screen,
            self.render_cfg.axis_color,
            self._world_to_screen(0.0, -half_h),
            self._world_to_screen(0.0, half_h),
            1,
        )

        spacing = 20.0
        tick_len_m = 1.0
        i_min = int(math.floor(-half_w / spacing))
        i_max = int(math.ceil(half_w / spacing))
        for i in range(i_min, i_max + 1):
            x = i * spacing
            p1 = self._world_to_screen(x, -tick_len_m)
            p2 = self._world_to_screen(x, tick_len_m)
            pygame.draw.line(self.screen, self.render_cfg.tick_color, p1, p2, 1)
            if self.font and i % 2 == 0:
                self._draw_text(f"{int(x)}", (x + 0.5, -2.5))

        j_min = int(math.floor(-half_h / spacing))
        j_max = int(math.ceil(half_h / spacing))
        for j in range(j_min, j_max + 1):
            y = j * spacing
   
            p1 = self._world_to_screen(-tick_len_m, y)
            p2 = self._world_to_screen(tick_len_m, y)
            pygame.draw.line(self.screen, self.render_cfg.tick_color, p1, p2, 1)
            if self.font and j % 2 == 0 and j != 0:
                self._draw_text(f"{int(y)}", (1.5, y - 2.5))

        self._draw_text("O", (1.5, -2.5))
        self._draw_text("X", (half_w - 4.0, 2.5))
        self._draw_text("Y", (2.5, half_h - 4.0))


def demo():
    """演示单艇APF控制"""
    env = USVEnv(dt=0.1)
    clock = pygame.time.Clock()
    running = True
    step_count = 0

    env.render()
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

        obs, reward, done, info = env.step()
        env.render()
        clock.tick(30)
        step_count += 1
        
        if done:
            print(f"Episode finished after {step_count} steps")
            print(f"Success: {info.get('success', False)}")
            print(f"Final reward: {reward}")
            env.reset()
            step_count = 0

    pygame.quit()


def collect_apf_data(num_episodes: int = 100):
    """收集APF轨迹数据（支持 Ctrl+C 安全退出）"""
    num_episodes = int(num_episodes)
    env = USVEnv(dt=0.1, collect_trajectories=True, max_episodes=num_episodes)
    print(f"开始收集 {num_episodes} 个回合的APF轨迹数据...")
    episode = 0
    try:
        while not env.trajectory_collector.is_collection_complete():
            obs, reward, done, info = env.step()

            if done:
                episode += 1
                print(f"完成回合 {episode}/{num_episodes}")
                if episode < num_episodes:
                    env.reset()
    except KeyboardInterrupt:
        # 尝试保存当前未完成回合的数据，避免数据丢失
        if env.collect_trajectories and env.trajectory_collector:
            env.trajectory_collector.save_episode()
        print("\n接收到 Ctrl+C，已安全退出并保存当前数据。")

    print("数据收集完成！")
    print(f"数据保存在: {env.trajectory_collector.data_dir}")

    return env.trajectory_collector.data_dir


if __name__ == "__main__":
    TAKE_DATA = 1
    if TAKE_DATA == 1:
        num_episodes = 3
        collect_apf_data(num_episodes)
    else:
        # 演示模式
        demo()
